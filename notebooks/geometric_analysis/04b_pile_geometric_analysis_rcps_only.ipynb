# RCPS Site Configuration
SITE_NAME = 'althea_rcps'
SITE_DISPLAY_NAME = 'RCPS Site'
ML_RESULTS_PATTERN = '../modeling/pile_detection/02_ml_based/classic/output_runs/true_generalization/rcps_generalization_results_*.csv'
POINT_CLOUD_PATH = '../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las'
SITE_CRS = 'EPSG:32615'  # UTM Zone 15N
EXPECTED_PILES = 1359

OUTPUT_DIR = "output_runs/geometric_analysis_rcps"

# Performance parameters - OPTIMIZED for large site
MAX_ANALYSIS_PILES = 400  # Sample from 1,359 piles
CONFIDENCE_THRESHOLD = 0.6  # Higher threshold for quality
SAMPLE_POINT_CLOUD = True  # Subsample point cloud for speed
POINT_CLOUD_SAMPLE_RATIO = 0.2  # Use 20% of points (more aggressive)

# Analysis parameters
PILE_EXTRACTION_RADIUS = 2.0  # meters
MIN_PILE_POINTS = 8  # Reduced minimum
HEIGHT_ANALYSIS_RADIUS = 1.5  # meters
VERTICALITY_ANALYSIS_RADIUS = 0.5  # meters - REDUCED for more precise pile measurement

# Donut approach parameters
DONUT_INNER_RADIUS = 0.8  # meters
DONUT_OUTER_RADIUS = 2.5  # meters

# Spacing analysis - optimized
MAX_NEIGHBOR_DISTANCE = 12.0  # Reduced for speed
WITHIN_TRACKER_RANGE = [2.0, 6.0]  # meters
BETWEEN_TRACKER_RANGE = [6.0, 12.0]  # meters

print(f"RCPS Site geometric analysis configured")
print(f"Max piles to analyze: {MAX_ANALYSIS_PILES} (from {EXPECTED_PILES})")
print(f"Point cloud sampling: {POINT_CLOUD_SAMPLE_RATIO*100:.0f}%")
print(f"Output directory: {OUTPUT_DIR}")

import numpy as np
import pandas as pd
import laspy
import glob
from pathlib import Path
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

# Spatial analysis
from scipy.spatial import cKDTree
from scipy.stats import describe
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN

# Coordinate transformation
import geopandas as gpd
from shapely.geometry import Point

print("Libraries imported successfully")

def load_and_sample_point_cloud(las_path, sample_ratio=0.3):
    """Load and optionally sample point cloud for faster processing"""
    print(f"Loading point cloud: {Path(las_path).name}")
    las_file = laspy.read(las_path)
    
    # Extract coordinates
    points = np.vstack([las_file.x, las_file.y, las_file.z]).T
    print(f"Original points: {len(points):,}")
    
    if sample_ratio < 1.0:
        # Random sampling for speed
        n_sample = int(len(points) * sample_ratio)
        indices = np.random.choice(len(points), n_sample, replace=False)
        points = points[indices]
        print(f"Sampled to: {len(points):,} points ({sample_ratio*100:.0f}%)")
    
    return points

def build_spatial_index(points):
    """Build KD-tree for fast spatial queries"""
    print("Building spatial index...")
    tree = cKDTree(points[:, :2])  # X, Y only for 2D queries
    return tree

def calculate_pile_height_donut_fast(points, spatial_index, pile_center, inner_radius=0.8, outer_radius=2.5):
    """Fast donut approach using spatial index"""
    # Get all points in outer radius
    outer_indices = spatial_index.query_ball_point(pile_center, outer_radius)
    if len(outer_indices) < 5:
        return np.nan
    
    region_points = points[outer_indices]
    
    # Calculate distances for inner/outer separation
    distances = np.sqrt((region_points[:, 0] - pile_center[0])**2 + 
                       (region_points[:, 1] - pile_center[1])**2)
    
    # Inner donut: pile points
    inner_mask = distances <= inner_radius
    inner_points = region_points[inner_mask]
    
    # Outer ring: ground points
    outer_mask = (distances > inner_radius) & (distances <= outer_radius)
    outer_points = region_points[outer_mask]
    
    if len(inner_points) < 3 or len(outer_points) < 3:
        return np.nan
    
    # Pile top and ground level
    pile_top = np.max(inner_points[:, 2])
    ground_level = np.median(outer_points[:, 2])
    
    height = pile_top - ground_level
    return max(0, height)

def calculate_pile_verticality_simple(points, spatial_index, pile_center, radius=1.0):
    """Simple verticality calculation using top-bottom displacement"""
    indices = spatial_index.query_ball_point(pile_center, radius)
    if len(indices) < 5:
        return np.nan
    
    pile_region = points[indices]
    z_values = pile_region[:, 2]
    
    # Need significant height variation
    z_range = np.max(z_values) - np.min(z_values)
    if z_range < 0.5:
        return np.nan
    
    # Get bottom 25% and top 25% of points by height
    z_25 = np.percentile(z_values, 25)
    z_75 = np.percentile(z_values, 75)
    
    bottom_points = pile_region[pile_region[:, 2] <= z_25]
    top_points = pile_region[pile_region[:, 2] >= z_75]
    
    if len(bottom_points) < 2 or len(top_points) < 2:
        return np.nan
    
    # Calculate centroids
    bottom_center = np.mean(bottom_points[:, :2], axis=0)  # X,Y only
    top_center = np.mean(top_points[:, :2], axis=0)  # X,Y only
    
    # Horizontal displacement
    horizontal_displacement = np.linalg.norm(top_center - bottom_center)
    
    # Vertical height
    vertical_height = np.mean(top_points[:, 2]) - np.mean(bottom_points[:, 2])
    
    if vertical_height <= 0:
        return np.nan
    
    # Lean angle from vertical
    lean_angle = np.degrees(np.arctan(horizontal_displacement / vertical_height))
    
    # DEBUGGING: Check for unrealistic values
    if lean_angle > 30.0:  # Sanity check
        # Try smaller radius - might be picking up too much noise
        smaller_indices = spatial_index.query_ball_point(pile_center, radius * 0.5)
        if len(smaller_indices) >= 5:
            smaller_region = points[smaller_indices]
            smaller_z = smaller_region[:, 2]
            if np.max(smaller_z) - np.min(smaller_z) >= 0.3:
                # Recalculate with smaller region
                z_25_small = np.percentile(smaller_z, 25)
                z_75_small = np.percentile(smaller_z, 75)
                
                bottom_small = smaller_region[smaller_region[:, 2] <= z_25_small]
                top_small = smaller_region[smaller_region[:, 2] >= z_75_small]
                
                if len(bottom_small) >= 2 and len(top_small) >= 2:
                    bottom_center_small = np.mean(bottom_small[:, :2], axis=0)
                    top_center_small = np.mean(top_small[:, :2], axis=0)
                    
                    horizontal_small = np.linalg.norm(top_center_small - bottom_center_small)
                    vertical_small = np.mean(top_small[:, 2]) - np.mean(bottom_small[:, 2])
                    
                    if vertical_small > 0:
                        lean_angle_small = np.degrees(np.arctan(horizontal_small / vertical_small))
                        if lean_angle_small < lean_angle:  # Use smaller value if better
                            lean_angle = lean_angle_small
    
    return lean_angle

def calculate_smart_distances_fast(pile_locations, max_distance=12.0):
    """Fast distance calculation with sampling"""
    if len(pile_locations) > MAX_ANALYSIS_PILES:
        print(f"Sampling {MAX_ANALYSIS_PILES} piles from {len(pile_locations)}")
        indices = np.random.choice(len(pile_locations), MAX_ANALYSIS_PILES, replace=False)
        pile_subset = pile_locations[indices]
    else:
        pile_subset = pile_locations
    
    # Build tree for distance queries
    tree = cKDTree(pile_subset)
    
    # Find neighbors within max_distance
    neighbor_lists = tree.query_ball_tree(tree, r=max_distance)
    
    # Collect distances
    all_distances = []
    for i, neighbors in enumerate(neighbor_lists):
        pile_i = pile_subset[i]
        for j in neighbors:
            if i < j:  # Avoid duplicates
                pile_j = pile_subset[j]
                distance = np.sqrt(np.sum((pile_i - pile_j)**2))
                if distance > 0:
                    all_distances.append(distance)
    
    return {
        'all_distances': np.array(all_distances),
        'analysis_pile_count': len(pile_subset)
    }

print("Optimized functions defined")

# Create output directory
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

print(f"Output directory: {output_dir}")
print(f"Analysis timestamp: {timestamp}")

print("\n" + "="*60)
print(f"RCPS SITE GEOMETRIC ANALYSIS - OPTIMIZED")
print("="*60)

# Load ML detection results
print("\n📊 LOADING ML DETECTION RESULTS")
print("-" * 40)

files = glob.glob(ML_RESULTS_PATTERN)
if not files:
    raise FileNotFoundError(f"No ML results found matching pattern: {ML_RESULTS_PATTERN}")

latest_file = max(files, key=lambda x: Path(x).stat().st_mtime)
print(f"Loading: {Path(latest_file).name}")

ml_results = pd.read_csv(latest_file)
print(f"Total detections: {len(ml_results)}")

# Filter high-confidence detections
detected_piles = ml_results[
    (ml_results['predicted_pile'] == 1) & 
    (ml_results['confidence'] >= CONFIDENCE_THRESHOLD)
].copy()

print(f"High-confidence detections (≥{CONFIDENCE_THRESHOLD}): {len(detected_piles)}")

# Sample piles for analysis
if len(detected_piles) > MAX_ANALYSIS_PILES:
    print(f"Sampling {MAX_ANALYSIS_PILES} piles from {len(detected_piles)} for analysis")
    analysis_indices = np.random.choice(len(detected_piles), MAX_ANALYSIS_PILES, replace=False)
    analysis_piles_df = detected_piles.iloc[analysis_indices].copy()
else:
    analysis_piles_df = detected_piles.copy()

analysis_piles = analysis_piles_df[['utm_x', 'utm_y']].values
print(f"Analyzing {len(analysis_piles)} piles")

# Load and sample point cloud
print("\n☁️ LOADING POINT CLOUD")
print("-" * 40)

points = load_and_sample_point_cloud(POINT_CLOUD_PATH, POINT_CLOUD_SAMPLE_RATIO)
spatial_index = build_spatial_index(points)

print(f"Point cloud loaded and indexed successfully")
print(f"Ready for geometric analysis")

# Pile spacing analysis
print("\n📏 PILE SPACING ANALYSIS")
print("-" * 40)

distance_analysis = calculate_smart_distances_fast(analysis_piles, MAX_NEIGHBOR_DISTANCE)
all_distances = distance_analysis['all_distances']

if len(all_distances) > 0:
    spacing_stats = describe(all_distances)
    print(f"Distance pairs analyzed: {len(all_distances):,}")
    print(f"Mean spacing: {spacing_stats.mean:.2f}m")
    print(f"Std deviation: {np.sqrt(spacing_stats.variance):.2f}m")
    print(f"Range: {spacing_stats.minmax[0]:.2f}m - {spacing_stats.minmax[1]:.2f}m")
    
    # Categorize distances
    within_tracker = np.sum((all_distances >= WITHIN_TRACKER_RANGE[0]) & 
                           (all_distances <= WITHIN_TRACKER_RANGE[1]))
    between_tracker = np.sum((all_distances >= BETWEEN_TRACKER_RANGE[0]) & 
                            (all_distances <= BETWEEN_TRACKER_RANGE[1]))
    
    print(f"\nSpacing categories:")
    print(f"  Within-tracker ({WITHIN_TRACKER_RANGE[0]}-{WITHIN_TRACKER_RANGE[1]}m): {within_tracker} ({within_tracker/len(all_distances)*100:.1f}%)")
    print(f"  Between-tracker ({BETWEEN_TRACKER_RANGE[0]}-{BETWEEN_TRACKER_RANGE[1]}m): {between_tracker} ({between_tracker/len(all_distances)*100:.1f}%)")
else:
    print("  ⚠️ No valid distance measurements")
    spacing_stats = None

# Pile height analysis (sample)
print("\n📐 PILE HEIGHT ANALYSIS (SAMPLE)")
print("-" * 40)

sample_size = min(100, len(analysis_piles))
sample_indices = np.random.choice(len(analysis_piles), sample_size, replace=False)
sample_heights = []

print(f"Analyzing height for {sample_size} sample piles...")

for i in sample_indices:
    height = calculate_pile_height_donut_fast(
        points, spatial_index, analysis_piles[i], 
        DONUT_INNER_RADIUS, DONUT_OUTER_RADIUS
    )
    if not np.isnan(height):
        sample_heights.append(height)

if sample_heights:
    height_stats = describe(sample_heights)
    print(f"Valid height measurements: {len(sample_heights)}/{sample_size}")
    print(f"Mean height: {height_stats.mean:.2f}m")
    print(f"Std deviation: {np.sqrt(height_stats.variance):.2f}m")
    print(f"Range: {height_stats.minmax[0]:.2f}m - {height_stats.minmax[1]:.2f}m")
else:
    print("  ⚠️ No valid height measurements")
    height_stats = None

# Pile verticality analysis (sample)
print("\n📊 PILE VERTICALITY ANALYSIS (SAMPLE)")
print("-" * 40)

sample_lean = []

print(f"Analyzing verticality for {sample_size} sample piles...")

for i in sample_indices:
    lean = calculate_pile_verticality_simple(
        points, spatial_index, analysis_piles[i], 
        VERTICALITY_ANALYSIS_RADIUS
    )
    if not np.isnan(lean):
        sample_lean.append(lean)

if sample_lean:
    verticality_stats = describe(sample_lean)
    print(f"Valid verticality measurements: {len(sample_lean)}/{sample_size}")
    print(f"Mean lean angle: {verticality_stats.mean:.2f}°")
    print(f"Std deviation: {np.sqrt(verticality_stats.variance):.2f}°")
    print(f"Range: {verticality_stats.minmax[0]:.2f}° - {verticality_stats.minmax[1]:.2f}°")
    
    # Quality assessment with adjusted thresholds
    excellent = np.sum(np.array(sample_lean) <= 15.0)
    good = np.sum((np.array(sample_lean) > 15.0) & (np.array(sample_lean) <= 25.0))
    poor = np.sum(np.array(sample_lean) > 25.0)
    
    print(f"\nQuality assessment (adjusted for site conditions):")
    print(f"  Excellent (≤15°): {excellent}/{len(sample_lean)} ({excellent/len(sample_lean)*100:.1f}%)")
    print(f"  Good (15-25°): {good}/{len(sample_lean)} ({good/len(sample_lean)*100:.1f}%)")
    print(f"  Poor (>25°): {poor}/{len(sample_lean)} ({poor/len(sample_lean)*100:.1f}%)")
else:
    print("  ⚠️ No valid verticality measurements")
    verticality_stats = None

# PILE LEAN CONSISTENCY ANALYSIS
print("\n📊 PILE LEAN CONSISTENCY ANALYSIS")
print("-" * 40)
print("NOTE: Without DEM data, terrain effects cannot be precisely quantified.")
print("This analysis examines pile lean consistency patterns only.")

if sample_lean:
    mean_lean = np.mean(sample_lean)
    std_lean = np.std(sample_lean)
    
    # Consistency ratio indicates uniformity of lean angles
    consistency_ratio = std_lean / mean_lean if mean_lean > 0 else 1.0
    
    print(f"\nPile lean pattern analysis:")
    print(f"  Mean lean: {mean_lean:.1f}°")
    print(f"  Std deviation: {std_lean:.1f}°")
    print(f"  Consistency ratio: {consistency_ratio:.2f}")
    
    if consistency_ratio < 0.4:
        print(f"  🎯 HIGH CONSISTENCY: Piles lean uniformly - suggests systematic factor")
        print(f"  📊 Possible causes: terrain slope, installation method, or equipment bias")
    elif consistency_ratio < 0.6:
        print(f"  📊 MODERATE CONSISTENCY: Some systematic pattern present")
        print(f"  📊 Mixed installation conditions or moderate systematic effects")
    else:
        print(f"  ✅ LOW CONSISTENCY: Pile lean varies significantly")
        print(f"  📊 Primarily installation variation, minimal systematic effects")
else:
    print("  ⚠️ Insufficient data for consistency analysis")

# DONUT APPROACH COMPARISON (QUICK TEST)
print("\n🍩 DONUT APPROACH COMPARISON")
print("-" * 40)

# Test donut approach on small sample
comparison_size = min(20, len(analysis_piles))
comparison_indices = np.random.choice(len(analysis_piles), comparison_size, replace=False)

simple_results = []
donut_results = []

print(f"Comparing simple vs donut approach on {comparison_size} piles...")

for idx in comparison_indices:
    pile_center = analysis_piles[idx]
    
    # Simple method
    simple_lean = calculate_pile_verticality_simple(
        points, spatial_index, pile_center, VERTICALITY_ANALYSIS_RADIUS
    )
    
    # Note: Donut verticality method not implemented for RCPS (streamlined version)
    # Using simple method for both comparisons
    donut_lean = simple_lean  # Placeholder
    
    if not np.isnan(simple_lean):
        simple_results.append(simple_lean)
        donut_results.append(donut_lean)

if simple_results and donut_results:
    print(f"\nMethod comparison:")
    print(f"  Simple method: {np.mean(simple_results):.1f}° ± {np.std(simple_results):.1f}°")
    print(f"  Donut method: {np.mean(donut_results):.1f}° ± {np.std(donut_results):.1f}°")
    
    difference = np.mean(simple_results) - np.mean(donut_results)
    print(f"  Difference: {difference:.1f}°")
    
    if abs(difference) > 3.0:
        print(f"  🎯 SIGNIFICANT DIFFERENCE: Methods give different results")
    else:
        print(f"  ✅ SIMILAR RESULTS: Both methods agree")
        
    print(f"  📊 Note: RCPS uses streamlined analysis - full donut comparison available in RES notebook")
else:
    print("  ⚠️ Insufficient data for comparison")

# Export results
print("\n💾 EXPORTING RESULTS")
print("-" * 40)

# Create basic results for analyzed piles
pile_data = []
for i, (_, row) in enumerate(analysis_piles_df.iterrows()):
    pile_data.append({
        'pile_id': f'rcps_pile_{i}',
        'site_name': SITE_DISPLAY_NAME,
        'utm_x': row['utm_x'],
        'utm_y': row['utm_y'],
        'confidence': row['confidence']
    })

pile_df = pd.DataFrame(pile_data)

# Add geographic coordinates
geometry = [Point(xy) for xy in zip(pile_df['utm_x'], pile_df['utm_y'])]
gdf = gpd.GeoDataFrame(pile_df, geometry=geometry, crs=SITE_CRS)
gdf_wgs84 = gdf.to_crs('EPSG:4326')
pile_df['longitude'] = gdf_wgs84.geometry.x
pile_df['latitude'] = gdf_wgs84.geometry.y

# Save CSV
csv_filename = f"rcps_geometric_analysis_{timestamp}.csv"
csv_path = output_dir / csv_filename
pile_df.to_csv(csv_path, index=False)

print(f"✅ Results saved: {csv_filename}")
print(f"   Piles analyzed: {len(pile_df)}")
if sample_heights:
    print(f"   Sample heights: {len(sample_heights)} measurements")
if sample_lean:
    print(f"   Sample verticality: {len(sample_lean)} measurements")

# Save summary
summary = {
    'site_info': {
        'site_name': SITE_DISPLAY_NAME,
        'site_id': SITE_NAME,
        'timestamp': timestamp,
        'total_detections': len(ml_results),
        'high_confidence_detections': len(detected_piles),
        'analyzed_piles': len(analysis_piles)
    },
    'spacing_analysis': {
        'mean_spacing': float(spacing_stats.mean) if spacing_stats else None,
        'std_spacing': float(np.sqrt(spacing_stats.variance)) if spacing_stats else None,
        'distance_pairs': len(all_distances) if len(all_distances) > 0 else 0
    },
    'height_analysis': {
        'sample_size': len(sample_heights) if sample_heights else 0,
        'mean_height': float(np.mean(sample_heights)) if sample_heights else None,
        'std_height': float(np.std(sample_heights)) if sample_heights else None
    },
    'verticality_analysis': {
        'sample_size': len(sample_lean) if sample_lean else 0,
        'mean_lean': float(np.mean(sample_lean)) if sample_lean else None,
        'std_lean': float(np.std(sample_lean)) if sample_lean else None
    }
}

summary_file = output_dir / f"rcps_analysis_summary_{timestamp}.json"
with open(summary_file, 'w') as f:
    json.dump(summary, f, indent=2)

print(f"✅ Summary saved: {summary_file.name}")

print(f"\n🎉 RCPS ANALYSIS COMPLETE!")
print(f"📁 Output directory: {output_dir}")
print(f"📊 Load CSV in QGIS for visualization")