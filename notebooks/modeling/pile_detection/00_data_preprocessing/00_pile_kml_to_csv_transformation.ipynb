{"cells": [{"cell_type": "markdown", "id": "8e103d9b", "metadata": {}, "source": ["## <PERSON>ad and Convert Pile KML Locations\n", "\n", "We load the pile location metadata provided as a KML file. The KML contains polygon geometries representing buffered zones around each pile. Since we need precise point locations for patch extraction, we convert these polygons to their centroids.\n", "\n", "We then transform the coordinate reference system from geographic (WGS 84) to UTM Zone 32N (EPSG:32632), which aligns with the point cloud coordinate system used for downstream processing.\n", "\n", "Finally, we extract the X and Y coordinates and set Z = 0 for now (to be filled in later using point cloud or IFC data), and save the pile metadata as a CSV.\n", "\n", "**Output:** `kml_piles_utm.csv` with columns `Name`, `Description`, `X`, `Y`, `Z`\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 4, "id": "6c3895ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "\n", "import geopandas as gpd\n", "import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 5, "id": "be3f3236", "metadata": {}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Standardized paths\n", "output_path = get_processed_data_path(site_name, f\"reference-data\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "CRS = \"EPSG:32632\""]}, {"cell_type": "code", "execution_count": 6, "id": "d65d30ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/reference-data\n", "Exported 1288 pile locations to kml_piles_utm.csv\n", "Coordinate ranges:\n", "X: 435628.57 to 436255.19\n", "Y: 5011261.36 to 5012200.75\n"]}], "source": ["# Load KML file\n", "kml_path = get_data_path(\"trino_enel\") / \"kml\" / \"pile.kml\"\n", "gdf = gpd.read_file(kml_path, driver='KML')\n", "\n", "# Reproject to UTM Zone 32N *before* computing centroids\n", "gdf = gdf.to_crs(CRS)\n", "\n", "# Convert polygons to points (centroids) in projected CRS\n", "gdf['geometry'] = gdf.geometry.centroid\n", "\n", "# Extract coordinates\n", "gdf['X'] = gdf.geometry.x\n", "gdf['Y'] = gdf.geometry.y\n", "gdf['Z'] = 0  # KML doesn't have Z, will be extracted later\n", "\n", "# Select relevant columns and export to CSV\n", "output_df = gdf[['Name', 'Description', 'X', 'Y', 'Z']].copy()\n", "output_df.to_csv(f'{output_path}/kml_piles_utm.csv', index=False)\n", "\n", "print(f\"Output path: {output_path}\")\n", "print(f\"Exported {len(output_df)} pile locations to kml_piles_utm.csv\")\n", "print(\"Coordinate ranges:\")\n", "print(f\"X: {output_df['X'].min():.2f} to {output_df['X'].max():.2f}\")\n", "print(f\"Y: {output_df['Y'].min():.2f} to {output_df['Y'].max():.2f}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}