import numpy as np
import pandas as pd
import pickle
import json
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.preprocessing import StandardScaler

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns

print("Libraries imported successfully")

# Configuration
DATA_PATH = "../../00_data_preprocessing/experimentation/output/ml_patch_data"
OUTPUT_DIR = "output_runs/fixed_classical_ml"
RANDOM_STATE = 42

# PointNet++ baseline to beat
POINTNET_BASELINE = {
    'accuracy': 0.905,
    'f1_score': 0.942,
    'precision': 0.923,
    'recall': 0.962
}

print(f"Target to beat - PointNet++ F1: {POINTNET_BASELINE['f1_score']:.3f}")

# Create output directory
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)

def load_patch_data(base_dir):
    """Load patch data and inspect structure"""
    patch_root = Path(base_dir).resolve()
    
    # Find the latest patch directory
    patch_dirs = sorted(patch_root.glob("patches_*"))
    latest_patch_dir = patch_dirs[-1]
    print(f"Using patch data from: {latest_patch_dir}")
    
    datasets = {}
    for split in ['train', 'val', 'test']:
        patch_file = latest_patch_dir / f"{split}_patches.pkl"
        meta_file = latest_patch_dir / f"{split}_metadata.json"
        
        with open(patch_file, 'rb') as f:
            patches = pickle.load(f)
        with open(meta_file, 'r') as f:
            metadata = json.load(f)
        
        datasets[split] = {'patches': patches, 'metadata': metadata}
        
        # Inspect data structure
        if len(patches) > 0:
            sample_patch = patches[0]
            print(f"  {split}: {len(patches)} patches, sample shape: {sample_patch.shape}")
        else:
            print(f"  {split}: {len(patches)} patches (empty)")
    
    return datasets

# Load data
print("Loading patch data...")
datasets = load_patch_data(DATA_PATH)
print("Data loading complete!")

def extract_classical_features(patches, metadata):
    """Extract classical ML features from 3D coordinate patch data"""
    features = []
    labels = []
    
    for patch, meta in zip(patches, metadata):
        if len(patch) == 0:
            continue
            
        # Extract 3D coordinates
        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]
        
        # Compute derived features
        radial_dist = np.sqrt(x**2 + y**2)
        height_above_min = z - np.min(z)
        
        # Statistical features (22 features total)
        feature_vector = [
            # Basic spatial statistics (9 features)
            np.mean(x), np.std(x), np.max(x) - np.min(x),
            np.mean(y), np.std(y), np.max(y) - np.min(y),
            np.mean(z), np.std(z), np.max(z) - np.min(z),
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distance features (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch),  # num_points
            np.std(x) / (np.std(y) + 1e-6),  # aspect_ratio
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),  # height_to_footprint_ratio
            np.percentile(radial_dist, 90),  # 90th percentile radial distance
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),  # fraction above mean height
        ]
        
        features.append(feature_vector)
        
        # Extract label
        if isinstance(meta, dict):
            label = meta.get('label', meta.get('patch_type') == 'positive')
        else:
            label = meta
        
        labels.append(int(label))
    
    return np.array(features, dtype=np.float32), np.array(labels, dtype=np.int64)

# Extract features
print("Extracting classical ML features...")
X_train, y_train = extract_classical_features(datasets['train']['patches'], datasets['train']['metadata'])
X_val, y_val = extract_classical_features(datasets['val']['patches'], datasets['val']['metadata'])
X_test, y_test = extract_classical_features(datasets['test']['patches'], datasets['test']['metadata'])

print(f"Feature extraction complete:")
print(f"  Train: {X_train.shape[0]} samples, {X_train.shape[1]} features")
print(f"  Val: {X_val.shape[0]} samples, {X_val.shape[1]} features")
print(f"  Test: {X_test.shape[0]} samples, {X_test.shape[1]} features")
print(f"  Class distribution - Train: {np.bincount(y_train)}, Test: {np.bincount(y_test)}")

# Feature scaling for SVM and Logistic Regression
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test_scaled = scaler.transform(X_test)

print("Feature scaling complete")

def evaluate_model(model, X_test, y_test, model_name):
    """Evaluate model and return metrics"""
    y_pred = model.predict(X_test)
    
    metrics = {
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred),
        'recall': recall_score(y_test, y_pred),
        'f1_score': f1_score(y_test, y_pred)
    }
    
    print(f"\n{model_name} Results:")
    print(f"  Accuracy: {metrics['accuracy']:.4f}")
    print(f"  Precision: {metrics['precision']:.4f}")
    print(f"  Recall: {metrics['recall']:.4f}")
    print(f"  F1-Score: {metrics['f1_score']:.4f}")
    
    return metrics, y_pred

# Train models
results = {}
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

print("Training classical ML models...")
print("=" * 50)

# Random Forest
print("Training Random Forest...")
rf_model = RandomForestClassifier(
    n_estimators=100, max_depth=20, min_samples_split=5, 
    min_samples_leaf=2, random_state=RANDOM_STATE
)
rf_model.fit(X_train, y_train)
rf_metrics, rf_pred = evaluate_model(rf_model, X_test, y_test, "Random Forest")
results['random_forest'] = {'model': rf_model, 'metrics': rf_metrics, 'predictions': rf_pred}

# Gradient Boosting
print("Training Gradient Boosting...")
gb_model = GradientBoostingClassifier(
    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=RANDOM_STATE
)
gb_model.fit(X_train, y_train)
gb_metrics, gb_pred = evaluate_model(gb_model, X_test, y_test, "Gradient Boosting")
results['gradient_boosting'] = {'model': gb_model, 'metrics': gb_metrics, 'predictions': gb_pred}

# SVM (using scaled data)
print("Training SVM...")
svm_model = SVC(kernel='rbf', C=1.0, gamma='scale', random_state=RANDOM_STATE)
svm_model.fit(X_train_scaled, y_train)
svm_metrics, svm_pred = evaluate_model(svm_model, X_test_scaled, y_test, "SVM")
results['svm'] = {'model': svm_model, 'metrics': svm_metrics, 'predictions': svm_pred, 'uses_scaled_data': True}

# Logistic Regression (using scaled data)
print("Training Logistic Regression...")
lr_model = LogisticRegression(C=1.0, max_iter=1000, random_state=RANDOM_STATE)
lr_model.fit(X_train_scaled, y_train)
lr_metrics, lr_pred = evaluate_model(lr_model, X_test_scaled, y_test, "Logistic Regression")
results['logistic'] = {'model': lr_model, 'metrics': lr_metrics, 'predictions': lr_pred, 'uses_scaled_data': True}

print("\nAll models trained successfully!")

# Create comparison table
comparison_data = []
for model_name, result in results.items():
    metrics = result['metrics']
    comparison_data.append({
        'Model': model_name.replace('_', ' ').title(),
        'Accuracy': f"{metrics['accuracy']:.4f}",
        'Precision': f"{metrics['precision']:.4f}",
        'Recall': f"{metrics['recall']:.4f}",
        'F1-Score': f"{metrics['f1_score']:.4f}"
    })

comparison_df = pd.DataFrame(comparison_data)
print("\n" + "=" * 60)
print("CLASSICAL ML MODELS PERFORMANCE COMPARISON")
print("=" * 60)
print(comparison_df.to_string(index=False))

# Compare with PointNet++ baseline
print("\n" + "=" * 60)
print("COMPARISON WITH POINTNET++ BASELINE")
print("=" * 60)
print(f"PointNet++ Baseline - F1: {POINTNET_BASELINE['f1_score']:.4f}, Accuracy: {POINTNET_BASELINE['accuracy']:.4f}")
print()

best_model = None
best_f1 = 0
for model_name, result in results.items():
    f1 = result['metrics']['f1_score']
    improvement = ((f1 - POINTNET_BASELINE['f1_score']) / POINTNET_BASELINE['f1_score']) * 100
    status = "- BEATS BASELINE" if f1 > POINTNET_BASELINE['f1_score'] else "- Below baseline"
    print(f"{model_name.replace('_', ' ').title()}: F1={f1:.4f} ({improvement:+.1f}%) {status}")
    
    if f1 > best_f1:
        best_f1 = f1
        best_model = model_name

print(f"\nBest Classical Model: {best_model.replace('_', ' ').title()} (F1: {best_f1:.4f})")

# Save trained models for validation
import joblib

print("\n" + "=" * 60)
print("SAVING TRAINED MODELS")
print("=" * 60)

model_files = {}

for model_name, result in results.items():
    # Save model
    model_path = output_dir / f"{model_name}_model_{timestamp}.pkl"
    joblib.dump(result['model'], model_path)
    model_files[model_name] = str(model_path)
    
    print(f"- Saved {model_name}: {model_path.name}")

# Save scaler for models that need it
scaler_path = output_dir / f"feature_scaler_{timestamp}.pkl"
joblib.dump(scaler, scaler_path)
print(f"- Saved feature scaler: {scaler_path.name}")

# Save model registry for easy loading
model_registry = {
    'timestamp': timestamp,
    'best_model': best_model,
    'best_f1_score': best_f1,
    'model_files': model_files,
    'scaler_file': str(scaler_path),
    'models_requiring_scaling': ['svm', 'logistic'],
    'feature_count': X_train.shape[1],
    'training_samples': len(X_train),
    'pointnet_baseline': POINTNET_BASELINE,
    'all_results': {name: result['metrics'] for name, result in results.items()}
}

registry_path = output_dir / f"model_registry_{timestamp}.json"
with open(registry_path, 'w') as f:
    json.dump(model_registry, f, indent=2)

print(f"- Model registry saved: {registry_path.name}")

print(f"\nSUMMARY:")
print(f"  Best model: {best_model} (F1: {best_f1:.4f})")
print(f"  Models saved: {len(model_files)}")
print(f"  Registry: {registry_path.name}")