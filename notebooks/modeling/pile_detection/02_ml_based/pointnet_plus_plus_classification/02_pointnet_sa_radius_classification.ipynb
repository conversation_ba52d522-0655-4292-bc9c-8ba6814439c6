{"cells": [{"cell_type": "markdown", "metadata": {"id": "hf02wH1cKVzW"}, "source": ["# PointNet++ <PERSON>le Classificaiton\n", "\n", "This notebook implements PointNet++ architecture for pile detection using the patch data prepared from the successful harmonization and extraction pipeline.\n", "\n", "**Architecture:**\n", "- Set abstraction layers for hierarchical feature learning\n", "- Radius-based ball query for local neighborhood grouping\n", "- Deeper classification head with dropout and regularization\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "markdown", "metadata": {"id": "W0ubzvdCLikt"}, "source": ["## Mount"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s482wPMALjl1", "outputId": "8068ad63-a3b7-43d7-9f2e-8c830c2cc6f0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "markdown", "metadata": {"id": "c2cZAcRbLkRz"}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "MHh2gLbSsAOq"}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch.cuda.amp import GradScaler, autocast\n", "import numpy as np\n", "import pandas as pd\n", "import pickle\n", "import os\n", "from pathlib import Path\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report\n", "import matplotlib.pyplot as plt\n", "import json\n", "import warnings\n", "import time\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {"id": "VobdCIv3KRzK"}, "source": ["## Configuration"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zFSF1kH3Jh0Z", "outputId": "f82cd2e0-46e1-41c6-f987-05a0efce460d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n", "Configuration:\n", "  batch_size: 8\n", "  num_epochs: 100\n", "  learning_rate: 0.0005\n", "  num_points: 1024\n", "  patience: 20\n", "  device: cuda\n", "  lr_scheduler: step\n", "  weight_decay: 0.001\n", "  dropout: 0.3\n", "  gradient_clip: 0.5\n", "  augmentation: False\n", "  use_amp: False\n"]}], "source": ["GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "\n", "project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER\n", "data_path = project_path / \"pointnet_data\"  # Same data as Iteration 1\n", "results_path = project_path / \"results_iter2\"\n", "models_path = project_path / \"models_iter2\"\n", "\n", "ifc_path = project_path / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "kml_path = project_path / \"pile.kml\"\n", "test_pkl_path = data_path / \"test_pointnet.pkl\"\n", "harmonized_pile_dataset = project_path/\"harmonized_pile_dataset_final.csv\"\n", "\n", "# Create directories\n", "for path in [results_path, models_path]:\n", "    path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Device configuration\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Training configuration\n", "# config = {\n", "#     # Base parameters (optimized from Iteration 1)\n", "#     'batch_size': 16 if device.type == 'cuda' else 8,\n", "#     'num_epochs': 100,\n", "#     'learning_rate': 0.001,\n", "#     'num_points': 512,\n", "#     'patience': 20,\n", "#     'device': device,\n", "\n", "#     # Parameters\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-4,\n", "#     'dropout': 0.5,\n", "#     'gradient_clip': 1.0,\n", "#     'augmentation': True,\n", "#     'use_amp': device.type == 'cuda'\n", "# }\n", "\n", "# config = {\n", "#     # Base parameters (optimized from Iteration 1)\n", "#     'batch_size': 8 if device.type == 'cuda' else 4,  # Smaller for stability\n", "#     'num_epochs': 100,\n", "#     'learning_rate': 0.0005,  # Keep same\n", "#     'num_points': 1024,  # Keep same for now\n", "#     'patience': 30,  # Increased patience\n", "#     'device': device,\n", "\n", "#     # Parameters\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-4,\n", "#     'dropout': 0.3,  # Reduced dropout\n", "#     'gradient_clip': 1.0,\n", "#     'augmentation': True,  # Keep but reduce in preprocessing\n", "#     'use_amp': device.type == 'cuda'\n", "# }\n", "\n", "# config = {\n", "#     'batch_size': 4 if device.type == 'cuda' else 2,  # Reduce batch size\n", "#     'num_epochs': 100,\n", "#     'learning_rate': 0.0005,\n", "#     'num_points': 2048,  # DOUBLE the points - this is the key!\n", "#     'patience': 30,\n", "#     'device': device,\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-4,\n", "#     'dropout': 0.3,\n", "#     'gradient_clip': 1.0,\n", "#     'augmentation': True,\n", "#     'use_amp': device.type == 'cuda'\n", "# }\n", "\n", "# config = {\n", "#     'batch_size': 4 if device.type == 'cuda' else 2,  # Good for 2048 points\n", "#     'num_epochs': 100,\n", "#     'learning_rate': 0.0005,\n", "#     'num_points': 2048,  # DOUBLE the points - this is the key!\n", "#     'patience': 30,\n", "#     'device': device,\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-4,\n", "#     'dropout': 0.3,\n", "#     'gradient_clip': 1.0,\n", "#     'augmentation': <PERSON><PERSON><PERSON>,  # DISABLE augmentation to start clean\n", "#     'use_amp': device.type == 'cuda'\n", "# }\n", "\n", "# config = {\n", "#     'batch_size': 4,\n", "#     'num_epochs': 100,\n", "#     'learning_rate': 0.01,  # MUCH higher LR for raw coordinates\n", "#     'num_points': 2048,\n", "#     'patience': 30,\n", "#     'device': device,\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-5,  # Much less regularization\n", "#     'dropout': 0.2,  # Less dropout\n", "#     'gradient_clip': 1.0,\n", "#     'augmentation': <PERSON><PERSON><PERSON>,\n", "#     'use_amp': device.type == 'cuda'\n", "# }\n", "\n", "# config = {\n", "#     'batch_size': 2,  # Very small batches\n", "#     'num_epochs': 100,\n", "#     'learning_rate': 0.00001,  # Much lower LR for raw coordinates\n", "#     'num_points': 512,  # Reduce points if needed\n", "#     'patience': 30,\n", "#     'device': device,\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-6,  # Very light regularization\n", "#     'dropout': 0.2,\n", "#     'gradient_clip': 0.1,  # Light clipping for large values\n", "#     'augmentation': <PERSON><PERSON><PERSON>,\n", "#     'use_amp': <PERSON><PERSON><PERSON>,  # Keep disabled\n", "# }\n", "\n", "# config = {\n", "#     'batch_size': 16,  # Very small for raw coordinates\n", "#     'num_epochs': 50,  # Fewer epochs for faster testing\n", "#     'learning_rate': 0.0001,  # Lower LR for raw coordinates\n", "#     'num_points': 128,  # Reduce points for speed\n", "#     'patience': 10,\n", "#     'device': device,\n", "#     'lr_scheduler': 'cosine',\n", "#     'weight_decay': 1e-4,\n", "#     'dropout': 0.3,\n", "#     'gradient_clip': 1.0,\n", "#     'augmentation': <PERSON><PERSON><PERSON>,\n", "#     'use_amp': False,  # Disable for raw coordinates\n", "# }\n", "\n", "# config = {\n", "#     'batch_size': 8,        # Smaller batches for stability\n", "#     'num_epochs': 100,      # More epochs\n", "#     'learning_rate': 0.0001, # MUCH lower learning rate\n", "#     'num_points': 256,      # More points (but manageable)\n", "#     'patience': 25,         # More patience\n", "#     'device': device,\n", "#     'lr_scheduler': 'step', # Simpler scheduler\n", "#     'weight_decay': 1e-3,   # More regularization\n", "#     'dropout': 0.5,         # More dropout\n", "#     'gradient_clip': 0.5,   # Lighter clipping\n", "#     'augmentation': <PERSON><PERSON><PERSON>,  # Disable for now\n", "#     'use_amp': <PERSON><PERSON><PERSON>,\n", "# }\n", "\n", "config = {\n", "    'batch_size': 8,\n", "    'num_epochs': 100,\n", "    'learning_rate': 0.0005,\n", "    'num_points': 1024,\n", "    'patience': 20,\n", "    'device': device,\n", "    'lr_scheduler': 'step',\n", "    'weight_decay': 0.001,\n", "    'dropout': 0.3,\n", "    'gradient_clip': 0.5,\n", "    'augmentation': <PERSON><PERSON><PERSON>,\n", "    'use_amp': <PERSON><PERSON><PERSON>,\n", "}\n", "\n", "print(\"Configuration:\")\n", "for key, value in config.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {"id": "xz8BanMTLyLD"}, "source": ["## Load and Preprocess Data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9ZXtwOuAJqK6", "outputId": "c36cb83c-44fe-41c6-8fdf-2c7fc9caaa4c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train: (1287, 1024, 20), labels: 1287\n", "val: (418, 1024, 20), labels: 418\n", "test: (418, 1024, 20), labels: 418\n", "(1287, 1024, 20)\n"]}], "source": ["def load_pile_data(data_path):\n", "    \"\"\"Load pile detection data from pickle files - Preserves 20 features for PointNet\"\"\"\n", "    datasets = {}\n", "    file_mapping = {\n", "        'train': 'train_pointnet.pkl',\n", "        'val': 'val_pointnet.pkl',\n", "        'test': 'test_pointnet.pkl'\n", "    }\n", "\n", "    for split, filename in file_mapping.items():\n", "        filepath = data_path / filename\n", "\n", "        if not filepath.exists():\n", "            print(f\"ERROR: {filepath} not found!\")\n", "            return None\n", "\n", "        with open(filepath, 'rb') as f:\n", "            data = pickle.load(f)\n", "\n", "        patches = data['points']\n", "        labels = data['labels']\n", "\n", "        # ✅ DO NOT TRUNCATE TO 3D\n", "        # if patches.shape[2] > 3:\n", "        #     patches = patches[:, :, :3]\n", "\n", "        patches = patches.astype(np.float32)\n", "        labels = np.array(labels)\n", "\n", "        print(f\"{split}: {patches.shape}, labels: {len(labels)}\")\n", "\n", "        datasets[split] = {\n", "            'patches': patches,\n", "            'labels': labels,\n", "            'metadata': data.get('metadata', [])\n", "        }\n", "\n", "    return datasets\n", "\n", "datasets = load_pile_data(data_path)\n", "print(datasets['train']['patches'].shape)  # Should be (N, 1024, 20)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "wLIUCScXL5cE"}, "outputs": [], "source": ["def augment_point_cloud(points, augment_prob=0.7):\n", "    \"\"\"NEW: Data augmentation for point clouds\"\"\"\n", "    if np.random.random() > augment_prob:\n", "        return points\n", "\n", "    # Random rotation around Z-axis\n", "    angle = np.random.uniform(0, 2 * np.pi)\n", "    cos_angle, sin_angle = np.cos(angle), np.sin(angle)\n", "    rotation_matrix = np.array([\n", "        [cos_angle, -sin_angle, 0],\n", "        [sin_angle, cos_angle, 0],\n", "        [0, 0, 1]\n", "    ])\n", "    points = points @ rotation_matrix.T\n", "\n", "    # Random scaling\n", "    scale = np.random.uniform(0.9, 1.1)\n", "    points = points * scale\n", "\n", "    # Random jittering\n", "    noise = np.random.normal(0, 0.01, points.shape)\n", "    points = points + noise\n", "\n", "    return points"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "YG3gkb_ML8y4"}, "outputs": [], "source": ["def preprocess_patches_1(patches, labels, num_points):\n", "    \"\"\"Preprocessing with augmentation\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Preprocessing of {len(patches)} patches...\")\n", "\n", "    for i, (patch, label) in enumerate(zip(patches, labels)):\n", "        # Remove zero-padded points\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 10:\n", "            continue\n", "\n", "        # Sample to fixed number of points\n", "        if len(valid_points) >= num_points:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=False)\n", "            sampled = valid_points[indices]\n", "        else:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=True)\n", "            sampled = valid_points[indices]\n", "            noise = np.random.normal(0, 0.01, sampled.shape)\n", "            sampled = sampled + noise\n", "\n", "        # Apply augmentation\n", "        if config['augmentation']:\n", "            sampled = augment_point_cloud(sampled, augment_prob=0.7)\n", "\n", "        # Center and normalize\n", "        centroid = np.mean(sampled, axis=0)\n", "        sampled = sampled - centroid\n", "\n", "        max_dist = np.max(np.linalg.norm(sampled, axis=1))\n", "        if max_dist > 1e-6:\n", "            sampled = sampled / max_dist\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Preprocessing complete: {len(processed_patches)} patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "W2ILjohKqM_F"}, "outputs": [], "source": ["def preprocess_patches_2(patches, labels, num_points):\n", "    \"\"\"Enhanced preprocessing inspired by 90%+ classical ML results\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Enhanced preprocessing of {len(patches)} patches...\")\n", "\n", "    for i, (patch, label) in enumerate(zip(patches, labels)):\n", "        # Remove zero-padded points\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 20:  # Higher threshold (was 10)\n", "            continue\n", "\n", "        # More aggressive outlier removal (classical ML technique)\n", "        distances = np.linalg.norm(valid_points, axis=1)\n", "        q90, q10 = np.percentile(distances, [90, 10])  # Remove extreme outliers\n", "        outlier_mask = (distances >= q10) & (distances <= q90)\n", "        clean_patch = valid_points[outlier_mask]\n", "\n", "        if len(clean_patch) < 15:\n", "            clean_patch = valid_points  # Fallback\n", "\n", "        # Smart sampling with pile-aware weighting\n", "        center_distances = np.linalg.norm(clean_patch[:, :2], axis=1)\n", "\n", "        # For piles: bias toward center, for non-piles: uniform sampling\n", "        if label == 1:  # Pile - bias toward center points\n", "            weights = 1.0 / (center_distances + 0.1)\n", "            weights = weights / weights.sum()\n", "        else:  # Non-pile - uniform sampling\n", "            weights = None\n", "\n", "        # Sample to fixed number of points\n", "        if len(clean_patch) >= num_points:\n", "            if weights is not None:\n", "                indices = np.random.choice(len(clean_patch), num_points,\n", "                                         replace=False, p=weights)\n", "            else:\n", "                indices = np.random.choice(len(clean_patch), num_points, replace=False)\n", "            sampled = clean_patch[indices]\n", "        else:\n", "            if weights is not None:\n", "                indices = np.random.choice(len(clean_patch), num_points,\n", "                                         replace=True, p=weights)\n", "            else:\n", "                indices = np.random.choice(len(clean_patch), num_points, replace=True)\n", "            sampled = clean_patch[indices]\n", "            noise = np.random.normal(0, 0.005, sampled.shape)  # Less noise\n", "            sampled = sampled + noise\n", "\n", "        # Apply minimal augmentation (much less aggressive)\n", "        if config['augmentation'] and np.random.random() < 0.3:  # Reduced from 0.7 to 0.3\n", "            sampled = augment_point_cloud(sampled, augment_prob=0.3)\n", "\n", "        # Robust centering and scaling\n", "        centroid = np.median(sampled, axis=0)  # More robust than mean\n", "        sampled = sampled - centroid\n", "\n", "        # Use 90th percentile for more stable scaling\n", "        distances_norm = np.linalg.norm(sampled, axis=1)\n", "        scale = np.percentile(distances_norm, 90)  # More robust than max\n", "        if scale > 1e-6:\n", "            sampled = sampled / scale\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Enhanced preprocessing complete: {len(processed_patches)} patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "b1Qak0VdHkZi"}, "outputs": [], "source": ["def preprocess_patches_3(patches, labels, num_points):\n", "    \"\"\"Minimal preprocessing to preserve spatial relationships like Classical ML\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Minimal preprocessing of {len(patches)} patches...\")\n", "\n", "    for i, (patch, label) in enumerate(zip(patches, labels)):\n", "        # Remove zero-padded points\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 20:\n", "            continue\n", "\n", "        # REMOVE the aggressive outlier removal - it might be removing important structure\n", "        # distances = np.linalg.norm(valid_points, axis=1)\n", "        # q90, q10 = np.percentile(distances, [90, 10])\n", "        # outlier_mask = (distances >= q10) & (distances <= q90)\n", "        # clean_patch = valid_points[outlier_mask]\n", "\n", "        # Use all valid points (like Classical ML used all features)\n", "        clean_patch = valid_points\n", "\n", "        # Remove the pile-aware weighting - it might be creating bias\n", "        # center_distances = np.linalg.norm(clean_patch[:, :2], axis=1)\n", "        # if label == 1:  # Pile - bias toward center points\n", "        #     weights = 1.0 / (center_distances + 0.1)\n", "        #     weights = weights / weights.sum()\n", "        # else:  # Non-pile - uniform sampling\n", "        #     weights = None\n", "\n", "        # Simple uniform sampling (like Classical ML)\n", "        if len(clean_patch) >= num_points:\n", "            indices = np.random.choice(len(clean_patch), num_points, replace=False)\n", "            sampled = clean_patch[indices]\n", "        else:\n", "            indices = np.random.choice(len(clean_patch), num_points, replace=True)\n", "            sampled = clean_patch[indices]\n", "            noise = np.random.normal(0, 0.002, sampled.shape)  # Minimal noise\n", "            sampled = sampled + noise\n", "\n", "        # NO augmentation to start clean\n", "        # if config['augmentation'] and np.random.random() < 0.3:\n", "        #     sampled = augment_point_cloud(sampled, augment_prob=0.3)\n", "\n", "        # MINIMAL normalization - preserve relative scales\n", "        centroid = np.mean(sampled, axis=0)  # Simple mean\n", "        sampled = sampled - centroid\n", "\n", "        # Light scaling to preserve spatial relationships\n", "        scale = np.std(np.linalg.norm(sampled, axis=1))  # Use std, not percentile\n", "        if scale > 1e-6:\n", "            sampled = sampled / (scale * 2)  # Less aggressive scaling\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Minimal preprocessing complete: {len(processed_patches)} patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "_d9jSV5YOQoX"}, "outputs": [], "source": ["def preprocess_patches_4(patches, labels, num_points):\n", "    \"\"\"Keep absolute coordinates - the key difference!\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Absolute coordinate preprocessing of {len(patches)} patches...\")\n", "\n", "    for patch, label in zip(patches, labels):\n", "        # Only remove zero-padding\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 20:\n", "            continue\n", "\n", "        # Sample points\n", "        if len(valid_points) >= num_points:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=False)\n", "            sampled = valid_points[indices]\n", "        else:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=True)\n", "            sampled = valid_points[indices]\n", "\n", "        # NO CENTERING - keep absolute coordinates!\n", "        # Light global scaling to prevent GPU issues\n", "        scaled = sampled / 50.0  # Scale by a fixed factor\n", "\n", "        processed_patches.append(scaled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Absolute coordinate preprocessing complete: {len(processed_patches)} patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "NmlB_iC8WduT"}, "outputs": [], "source": ["def preprocess_patches_5(patches, labels, num_points):\n", "    \"\"\"Light scaling - preserves height differences\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    for patch, label in zip(patches, labels):\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 20:\n", "            continue\n", "\n", "        # Sample points\n", "        if len(valid_points) >= num_points:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=False)\n", "            sampled = valid_points[indices]\n", "        else:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=True)\n", "            sampled = valid_points[indices]\n", "\n", "        # ONLY light global scaling - NO CENTERING!\n", "        scaled = sampled / 30.0  # Keeps piles ~2.0 height, non-piles ~1.6\n", "\n", "        processed_patches.append(scaled)\n", "        processed_labels.append(label)\n", "\n", "    return np.array(processed_patches), np.array(processed_labels)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "IFIgc_hmzXJb"}, "outputs": [], "source": ["def preprocess_patches_6(patches, labels, num_points):\n", "    \"\"\"Preserve height differences - the key discriminative feature\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Height-preserving preprocessing of {len(patches)} patches...\")\n", "\n", "    for patch, label in zip(patches, labels):\n", "        # Remove zero-padded points\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 20:\n", "            continue\n", "\n", "        # Sample points\n", "        if len(valid_points) >= num_points:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=False)\n", "            sampled = valid_points[indices]\n", "        else:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=True)\n", "            sampled = valid_points[indices]\n", "            # Minimal noise for upsampled points\n", "            noise = np.random.normal(0, 0.01, sampled.shape)\n", "            sampled = sampled + noise\n", "\n", "        # KEY CHANGE: Only center X,Y coordinates, preserve Z (height)\n", "        xy_centroid = np.mean(sampled[:, :2], axis=0)\n", "        sampled[:, :2] = sampled[:, :2] - xy_centroid\n", "\n", "        # Light global scaling (preserves height differences)\n", "        sampled = sampled / 20.0  # Much lighter than current /30.0\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Height-preserving preprocessing complete: {len(processed_patches)} patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "r6eMDF9i7HG3"}, "outputs": [], "source": ["def preprocess_patches_7(patches, labels, num_points):\n", "\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    for patch, label in zip(patches, labels):\n", "        patch = np.array(patch, dtype=np.float32)\n", "\n", "        # Simple sampling\n", "        if len(patch) >= num_points:\n", "            sampled = patch[np.random.choice(len(patch), num_points, replace=False)]\n", "        else:\n", "            # Upsample with light noise\n", "            extra = np.stack([\n", "                patch[np.random.randint(len(patch))] + np.random.normal(0, 0.01, 3)\n", "                for _ in range(num_points - len(patch))\n", "            ])\n", "            sampled = np.vstack([patch, extra])\n", "\n", "        # ✅ ONLY normalize by max distance - preserves relationships\n", "        sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    return np.array(processed_patches), np.array(processed_labels)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "tx8BC0cbOdh-"}, "outputs": [], "source": ["import numpy as np\n", "\n", "def preprocess_patches(patches, labels, num_points=1024, num_features=20):\n", "    \"\"\"\n", "    Preprocess point cloud patches for PointNet training.\n", "\n", "    Parameters:\n", "    - patches: List or array of shape (N, variable_points, num_features)\n", "    - labels: Corresponding labels for each patch\n", "    - num_points: Target number of points per patch\n", "    - num_features: Expected number of features per point (default: 20)\n", "\n", "    Returns:\n", "    - processed_patches: Array of shape (N, num_points, num_features)\n", "    - processed_labels: Array of shape (N,)\n", "    \"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    for patch, label in zip(patches, labels):\n", "        patch = np.array(patch, dtype=np.float32)\n", "\n", "        if patch.shape[1] != num_features:\n", "            raise ValueError(f\"Expected {num_features} features, got {patch.shape[1]}\")\n", "\n", "        if len(patch) >= num_points:\n", "            distances = patch[:, 4]  # distance_norm\n", "            probabilities = 1 / (distances + 0.1)\n", "            probabilities /= probabilities.sum()\n", "\n", "            sampled_indices = np.random.choice(len(patch), num_points, replace=False, p=probabilities)\n", "            sampled = patch[sampled_indices]\n", "        else:\n", "            upsampled = patch.copy()\n", "            needed = num_points - len(patch)\n", "\n", "            for _ in range(needed):\n", "                distances = patch[:, 4]\n", "                weights = 1 / (distances + 0.1)\n", "                weights /= weights.sum()\n", "                source_idx = np.random.choice(len(patch), p=weights)\n", "\n", "                new_point = patch[source_idx].copy()\n", "                new_point[:3] += np.random.normal(0, 0.02, 3)\n", "                upsampled = np.vstack([upsampled, new_point])\n", "\n", "            sampled = upsampled[:num_points]\n", "\n", "        spatial_coords = sampled[:, :3]\n", "        spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "        if spatial_extent > 0:\n", "            sampled[:, :3] /= spatial_extent\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    return np.array(processed_patches), np.array(processed_labels)\n", "\n", "\n", "def preprocess_patches_simple_fix(patches, labels, num_points=1024):\n", "    \"\"\"\n", "    Simpler fallback for preprocessing without full sampling logic.\n", "    \"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    for patch, label in zip(patches, labels):\n", "        patch = np.array(patch, dtype=np.float32)\n", "        n_points, n_features = patch.shape\n", "\n", "        if n_points >= num_points:\n", "            indices = np.random.choice(n_points, num_points, replace=False)\n", "            sampled = patch[indices]\n", "        else:\n", "            extra_indices = np.random.choice(n_points, num_points - n_points, replace=True)\n", "            extra = patch[extra_indices].copy()\n", "            if n_features >= 3:\n", "                extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))\n", "            sampled = np.vstack([patch, extra])\n", "\n", "        if sampled.shape[1] >= 3:\n", "            spatial_coords = sampled[:, :3]\n", "            max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "            if max_dist > 0:\n", "                sampled[:, :3] /= max_dist\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    return np.array(processed_patches), np.array(processed_labels)\n", "\n", "\n", "def verify_preprocessing_consistency(patches_sample):\n", "    \"\"\"\n", "    Verifies the consistency of processed patches.\n", "    \"\"\"\n", "    patches_array = np.array(patches_sample)\n", "\n", "    print(\"Preprocessing Verification:\")\n", "    print(f\"   Shape: {patches_array.shape}\")\n", "    print(\"   Expected: (N, 1024, 20)\")\n", "\n", "    if len(patches_array.shape) == 3 and patches_array.shape[2] == 20:\n", "        print(\"   Status: PASS - Matches expected input format.\")\n", "        sample_patch = patches_array[0]\n", "        print(\"   Feature Ranges:\")\n", "        print(f\"     Spatial (0–2): {sample_patch[:, :3].min():.3f} to {sample_patch[:, :3].max():.3f}\")\n", "        print(f\"     Height Norm (3): {sample_patch[:, 3].min():.3f} to {sample_patch[:, 3].max():.3f}\")\n", "        print(f\"     Distance Norm (4): {sample_patch[:, 4].min():.3f} to {sample_patch[:, 4].max():.3f}\")\n", "        print(f\"     Density (5): {sample_patch[:, 5].min():.3f} to {sample_patch[:, 5].max():.3f}\")\n", "        return True\n", "    else:\n", "        print(\"   Status: FAIL - Feature count mismatch.\")\n", "        return False\n"]}, {"cell_type": "markdown", "metadata": {"id": "MPNQRo5lL_YA"}, "source": ["## PointNet++ Architecture"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "Qx8uPtyMJ75Y"}, "outputs": [], "source": ["def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between points\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "\n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "\n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"NEW: Ball query for radius-based local neighborhoods\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "H5RyiXGDMGdo"}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    \"\"\"NEW: Standard PointNet++ Set Abstraction Layer\"\"\"\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "\n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "\n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "\n", "        return new_xyz, new_points\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "uBkaXMcMMIG_"}, "outputs": [], "source": ["class PointNetPlusPlus(nn.Module):\n", "    # Modidied original Pointnet plus which is designed for only 3\n", "    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set Abstraction Layers\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(dropout)\n", "\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(dropout)\n", "\n", "        self.fc3 = nn.Linear(256, 64)\n", "        self.bn3 = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "        self.drop3 = nn.Dropout(dropout * 0.6)\n", "\n", "        self.fc4 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        # Input shape: (B, N, C), C = in_channels\n", "        if len(xyz.shape) == 4:\n", "            xyz = xyz.squeeze(1)  # Remove extra dim if present\n", "\n", "        B, N, C = xyz.shape\n", "\n", "        # Split input into xyz coords and features\n", "        coords = xyz[:, :, :3]       # (B, N, 3)\n", "        features = xyz[:, :, 3:]     # (B, N, C-3) or None if C == 3\n", "\n", "        # Pass through SA layers\n", "        l1_xyz, l1_points = self.sa1(coords, features)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        global_feat = l3_points.view(B, -1)\n", "\n", "        # Classification head\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))\n", "        x = self.fc4(x)\n", "\n", "        return x\n"]}, {"cell_type": "markdown", "metadata": {"id": "Qg1La78JMQbr"}, "source": ["## Dataset Class"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "YkBSpM_MJ4H1"}, "outputs": [], "source": ["class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]"]}, {"cell_type": "markdown", "metadata": {"id": "nGpCV3gtMTab"}, "source": ["## Training and Evaluation Epoch Functions"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "-edyo8STJ28l"}, "outputs": [], "source": ["def setup_training(model, train_labels):\n", "    \"\"\"Balanced training setup like Classical ML\"\"\"\n", "\n", "    # Check data distribution\n", "    pile_count = np.sum(train_labels)\n", "    total_count = len(train_labels)\n", "    pile_ratio = pile_count / total_count\n", "    print(f\"Data distribution: {total_count-pile_count} non-piles, {pile_count} piles\")\n", "    print(f\"Pile ratio: {pile_ratio:.3f}\")\n", "\n", "    # Light class weighting (not aggressive)\n", "    if pile_ratio > 0.6:  # You have 66.9% piles\n", "        # Weight the minority class slightly more\n", "        pos_weight = 1.2  # Light weighting\n", "        neg_weight = 0.8\n", "        class_weights = torch.FloatTensor([neg_weight, pos_weight]).to(device)\n", "        criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "    else:\n", "        criterion = nn.CrossEntropyLoss()\n", "\n", "    print(f\"Using class weights: {class_weights if 'class_weights' in locals() else None}\")\n", "\n", "    # More conservative optimizer\n", "    optimizer = optim.<PERSON>(  # Switch to <PERSON>\n", "        model.parameters(),\n", "        lr=config['learning_rate'],\n", "        weight_decay=config['weight_decay']\n", "    )\n", "\n", "    # Step scheduler instead of cosine\n", "    scheduler = optim.lr_scheduler.StepLR(\n", "        optimizer, step_size=20, gamma=0.5\n", "    )\n", "\n", "    return criterion, optimizer, scheduler"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "OOCyBhCEJ1Kr"}, "outputs": [], "source": ["def train_epoch(model, loader, criterion, optimizer, device, scaler=None):\n", "    \"\"\"Training with gradient clipping\"\"\"\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "        optimizer.zero_grad()\n", "\n", "        if scaler is not None:\n", "            with autocast():\n", "                output = model(data)\n", "                loss = criterion(output, target)\n", "            scaler.scale(loss).backward()\n", "            # NEW: Gradient clipping\n", "            scaler.unscale_(optimizer)\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "        else:\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            loss.backward()\n", "            # NEW: Gradient clipping\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])\n", "            optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "xPalwld4JzAj"}, "outputs": [], "source": ["def validate_epoch(model, loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "\n", "            if config['use_amp']:\n", "                with autocast():\n", "                    output = model(data)\n", "                    loss = criterion(output, target)\n", "            else:\n", "                output = model(data)\n", "                loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total"]}, {"cell_type": "markdown", "metadata": {"id": "FASJqJhcMlxO"}, "source": ["## Training Loop"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ayvVE5smJwLy", "outputId": "31ceb0ba-6eba-4711-a327-15cdb04f01c9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["POINTNET++ PILE DETECTION - ITERATION 2\n", "==================================================\n", "- Proper Set Abstraction layers with ball query\n", "- Data augmentation (rotation, scaling, jittering)\n", "- Learning rate scheduling (Cosine Annealing)\n", "- Gradient clipping for stability\n", "- Optimizer (AdamW) with weight decay\n", "- Deeper classification head (4 layers)\n", "==================================================\n"]}], "source": ["print(\"POINTNET++ PILE DETECTION - ITERATION 2\")\n", "print(\"=\"*50)\n", "print(\"- Proper Set Abstraction layers with ball query\")\n", "print(\"- Data augmentation (rotation, scaling, jittering)\")\n", "print(\"- Learning rate scheduling (Cosine Annealing)\")\n", "print(\"- Gradient clipping for stability\")\n", "print(\"- Optimizer (AdamW) with weight decay\")\n", "print(\"- Deeper classification head (4 layers)\")\n", "print(\"=\"*50)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "S0q33-K7Np0u"}, "outputs": [], "source": ["def run_training(model, train_loader, val_loader, criterion, optimizer, scheduler, config, scaler, models_path):\n", "    train_losses = []\n", "    val_losses = []\n", "    train_accs = []\n", "    val_accs = []\n", "    learning_rates = []\n", "\n", "    best_val_loss = float('inf')  # Track validation LOSS, not just accuracy\n", "    best_val_acc = 0\n", "    patience_counter = 0\n", "    start_time = time.time()\n", "\n", "    for epoch in range(config['num_epochs']):\n", "        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device, scaler)\n", "        val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "\n", "        current_lr = optimizer.param_groups[0]['lr']\n", "        learning_rates.append(current_lr)\n", "\n", "        if scheduler:\n", "            scheduler.step()\n", "\n", "        train_losses.append(train_loss)\n", "        val_losses.append(val_loss)\n", "        train_accs.append(train_acc)\n", "        val_accs.append(val_acc)\n", "\n", "        if (epoch + 1) % 10 == 0:\n", "            print(f\"Epoch {epoch+1}/{config['num_epochs']}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}, Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}, LR={current_lr:.6f}\")\n", "\n", "        # IMPROVED EARLY STOPPING: Use validation loss + accuracy\n", "        improved = False\n", "        if val_loss < best_val_loss:\n", "            best_val_loss = val_loss\n", "            improved = True\n", "        if val_acc > best_val_acc:\n", "            best_val_acc = val_acc\n", "            improved = True\n", "\n", "        if improved:\n", "            patience_counter = 0\n", "            torch.save({\n", "                'model_state_dict': model.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "                'epoch': epoch,\n", "                'val_acc': val_acc,\n", "                'val_loss': val_loss,\n", "                'config': config\n", "            }, models_path / 'best_pointnet_iter4.pth')\n", "        else:\n", "            patience_counter += 1\n", "            if patience_counter >= config['patience']:\n", "                print(f\"Early stopping at epoch {epoch+1} (no improvement for {config['patience']} epochs)\")\n", "                break\n", "\n", "    training_time = time.time() - start_time\n", "    print(f\"Training completed in {training_time:.1f}s\")\n", "    print(f\"Best validation accuracy: {best_val_acc:.4f}\")\n", "    print(f\"Best validation loss: {best_val_loss:.4f}\")\n", "\n", "    history = {\n", "        'train_losses': train_losses,\n", "        'val_losses': val_losses,\n", "        'train_accs': train_accs,\n", "        'val_accs': val_accs,\n", "        'learning_rates': learning_rates\n", "    }\n", "\n", "    return history, best_val_acc, training_time\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VkUsTcAc96t2", "outputId": "16c8829e-2b96-4d65-bc49-0d8466b85fe5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train: (1287, 1024, 20), labels: 1287\n", "val: (418, 1024, 20), labels: 418\n", "test: (418, 1024, 20), labels: 418\n"]}], "source": ["# Load data\n", "datasets = load_pile_data(data_path)\n", "if not datasets:\n", "    print(\"ERROR: No data found!\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "llA7sSA5NNu5"}, "outputs": [], "source": ["# Preprocess data\n", "train_patches, train_labels = preprocess_patches(\n", "    datasets['train']['patches'], datasets['train']['labels'], config['num_points'],num_features=20\n", ")\n", "val_patches, val_labels = preprocess_patches(\n", "    datasets['val']['patches'], datasets['val']['labels'], config['num_points'],num_features=20\n", ")\n", "test_patches, test_labels = preprocess_patches(\n", "    datasets['test']['patches'], datasets['test']['labels'], config['num_points'],num_features=20\n", ")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7Tli0qoeRsnF", "outputId": "9cb763e6-3162-4a34-8f63-d56f6b74cb47"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== VERIFYING DATA INTEGRITY ===\n", "Original data range: [-0.60, 105.06]\n", "Processed data range: [-0.60, 12.34]\n", "Original data intact - no corruption detected\n", "Processed data range looks unusual\n"]}], "source": ["# === DATA INTEGRITY VERIFICATION ===\n", "print(\"\\n=== VERIFYING DATA INTEGRITY ===\")\n", "original_max = datasets['train']['patches'].max()\n", "original_min = datasets['train']['patches'].min()\n", "processed_max = train_patches.max()\n", "processed_min = train_patches.min()\n", "\n", "print(f\"Original data range: [{original_min:.2f}, {original_max:.2f}]\")\n", "print(f\"Processed data range: [{processed_min:.2f}, {processed_max:.2f}]\")\n", "\n", "if original_max > 50:  # Should be ~60 if uncorrupted\n", "    print(\"Original data intact - no corruption detected\")\n", "else:\n", "    print(\"WARNING: Original data may be corrupted!\")\n", "\n", "if processed_max < 2:  # Good preprocessing should be 0-1 range\n", "    print(\"Processed data in good range\")\n", "else:\n", "    print(\"Processed data range looks unusual\")\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "OGhXwA3L-FJs"}, "outputs": [], "source": ["# Create datasets and loaders\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, drop_last=True)\n", "val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JlmidFx6-Bia", "outputId": "c30c1740-2379-4b0f-f1ac-d32b518081e4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PREPROCESSING FUNCTION DEBUG ===\n", "Original patch: range=[-0.00, 60.07], std=12.805\n", "Valid points: 1024, range=[-0.00, 60.07], std=12.805\n", "After sampling: range=[-0.00, 60.07], std=12.805\n", "After normalization (actual preprocessing): range=[-0.00, 0.99], std=0.211\n"]}], "source": ["# === PREPROCESSING FUNCTION DEBUG ===\n", "print(\"\\n=== PREPROCESSING FUNCTION DEBUG ===\")\n", "\n", "# Test the function directly on one sample\n", "test_patch = datasets['train']['patches'][0].copy()  # Get original patch (SAFE COPY)\n", "test_label = datasets['train']['labels'][0]\n", "\n", "print(f\"Original patch: range=[{test_patch.min():.2f}, {test_patch.max():.2f}], std={np.std(test_patch):.3f}\")\n", "\n", "# Apply your preprocessing function to this single patch\n", "valid_mask = np.abs(test_patch).sum(axis=1) > 1e-6\n", "valid_points = test_patch[valid_mask]\n", "print(f\"Valid points: {len(valid_points)}, range=[{valid_points.min():.2f}, {valid_points.max():.2f}], std={np.std(valid_points):.3f}\")\n", "\n", "# Sample points\n", "indices = np.random.choice(len(valid_points), min(config['num_points'], len(valid_points)), replace=False)\n", "sampled = valid_points[indices]\n", "print(f\"After sampling: range=[{sampled.min():.2f}, {sampled.max():.2f}], std={np.std(sampled):.3f}\")\n", "\n", "# Apply the SAME normalization as actual preprocessing\n", "normalized = sampled / np.max(np.linalg.norm(sampled, axis=1))\n", "print(f\"After normalization (actual preprocessing): range=[{normalized.min():.2f}, {normalized.max():.2f}], std={np.std(normalized):.3f}\")\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vFbsAM1eOAWI", "outputId": "93ed076d-4f89-41c6-f7fc-ae34f7a55c20"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAMPLE DATA ANALYSIS ===\n", "After preprocessing:\n", "Pile samples height range: 0.024 to 0.037\n", "Non-pile samples height range: 0.004 to 0.038\n", "Height difference preserved: 0.010\n", "<PERSON><PERSON> samples stats:\n", "  Sample 0: mean=1.493779, std=1.452432\n", "  Sample 1: mean=1.470052, std=1.386878\n", "  Sample 2: mean=1.420018, std=1.398345\n", "  Sample 3: mean=1.427747, std=1.379081\n", "  Sample 4: mean=1.407127, std=1.322659\n", "Non-pile samples stats:\n", "  Sample 0: mean=1.030109, std=1.301644\n", "  Sample 1: mean=0.975331, std=1.322136\n", "  Sample 2: mean=1.357652, std=1.280153\n", "  Sample 3: mean=1.454033, std=1.369145\n", "  Sample 4: mean=1.013713, std=1.320691\n", "\n", "Data variance analysis:\n", "Pile samples variance: 1.929001\n", "Non-pile samples variance: 1.779520\n", "Pile sample range: [-0.233503, 5.589275]\n", "Non-pile sample range: [-0.083570, 5.150491]\n"]}], "source": ["# === SAMPLE DATA ANALYSIS ===\n", "print(\"\\n=== SAMPLE DATA ANALYSIS ===\")\n", "\n", "# Check actual data differences\n", "pile_samples = train_patches[train_labels == 1][:5]\n", "non_pile_samples = train_patches[train_labels == 0][:5]\n", "\n", "print(\"After preprocessing:\")\n", "print(f\"Pile samples height range: {pile_samples[:,:,2].min():.3f} to {pile_samples[:,:,2].max():.3f}\")\n", "print(f\"Non-pile samples height range: {non_pile_samples[:,:,2].min():.3f} to {non_pile_samples[:,:,2].max():.3f}\")\n", "print(f\"Height difference preserved: {abs(pile_samples[:,:,2].mean() - non_pile_samples[:,:,2].mean()):.3f}\")\n", "\n", "print(f\"<PERSON><PERSON> samples stats:\")\n", "for i, sample in enumerate(pile_samples):\n", "    print(f\"  Sample {i}: mean={np.mean(sample):.6f}, std={np.std(sample):.6f}\")\n", "\n", "print(f\"Non-pile samples stats:\")\n", "for i, sample in enumerate(non_pile_samples):\n", "    print(f\"  Sample {i}: mean={np.mean(sample):.6f}, std={np.std(sample):.6f}\")\n", "\n", "# Check if samples look identical\n", "print(f\"\\nData variance analysis:\")\n", "print(f\"Pile samples variance: {np.var(pile_samples):.6f}\")\n", "print(f\"Non-pile samples variance: {np.var(non_pile_samples):.6f}\")\n", "\n", "# Check for identical preprocessing\n", "print(f\"Pile sample range: [{pile_samples.min():.6f}, {pile_samples.max():.6f}]\")\n", "print(f\"Non-pile sample range: [{non_pile_samples.min():.6f}, {non_pile_samples.max():.6f}]\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d3daagLXPOY_", "outputId": "a5321e34-d89f-4c3f-cc0f-d04093646c8c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ORIGINAL DATA ANALYSIS (BEFORE PREPROCESSING) ===\n", "Original PILE samples (before preprocessing):\n", "  Pile 0: 1024 points, range=[-0.00, 60.07], std=12.805\n", "  Pile 1: 1024 points, range=[-0.01, 50.07], std=10.635\n", "  Pile 2: 1024 points, range=[-0.23, 47.05], std=10.006\n", "  Pile 3: 1024 points, range=[-0.05, 52.07], std=11.082\n", "  Pile 4: 1024 points, range=[-0.02, 52.07], std=11.076\n", "Original NON-PILE samples (before preprocessing):\n", "  Non-pile 0: 1024 points, range=[-0.04, 39.06], std=8.368\n", "  Non-pile 1: 1024 points, range=[-0.02, 48.07], std=10.329\n", "  Non-pile 2: 1024 points, range=[-0.04, 38.06], std=8.062\n", "  Non-pile 3: 1024 points, range=[-0.02, 47.09], std=9.992\n", "  Non-pile 4: 1024 points, range=[-0.08, 44.06], std=9.453\n"]}], "source": ["# === ORIGINAL DATA ANALYSIS (BEFORE PREPROCESSING) ===\n", "print(\"\\n=== ORIGINAL DATA ANALYSIS (BEFORE PREPROCESSING) ===\")\n", "\n", "# Get original patches directly from datasets\n", "train_original = datasets['train']['patches'].copy()\n", "train_labels_orig = datasets['train']['labels'].copy()\n", "\n", "# Check original pile vs non-pile samples\n", "pile_indices = np.where(np.array(train_labels_orig) == 1)[0][:5]\n", "non_pile_indices = np.where(np.array(train_labels_orig) == 0)[0][:5]\n", "\n", "print(\"Original PILE samples (before preprocessing):\")\n", "for i, idx in enumerate(pile_indices):\n", "    sample = train_original[idx]\n", "    valid_mask = np.abs(sample).sum(axis=1) > 1e-6\n", "    valid_points = sample[valid_mask]\n", "    print(f\"  Pile {i}: {len(valid_points)} points, range=[{valid_points.min():.2f}, {valid_points.max():.2f}], std={np.std(valid_points):.3f}\")\n", "\n", "print(\"Original NON-PILE samples (before preprocessing):\")\n", "for i, idx in enumerate(non_pile_indices):\n", "    sample = train_original[idx]\n", "    valid_mask = np.abs(sample).sum(axis=1) > 1e-6\n", "    valid_points = sample[valid_mask]\n", "    print(f\"  Non-pile {i}: {len(valid_points)} points, range=[{valid_points.min():.2f}, {valid_points.max():.2f}], std={np.std(valid_points):.3f}\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2YT3PEHcNR8j", "outputId": "19b0b4e6-d410-49c5-a88e-aa4009f20c62"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data distribution: 426 non-piles, 861 piles\n", "Pile ratio: 0.669\n", "Using class weights: tensor([0.8000, 1.2000], device='cuda:0')\n", "Model parameters: 1,482,434\n"]}], "source": ["# Initialize model with more regularization\n", "# model = PointNetPlusPlus(num_classes=2).to(device)\n", "model = PointNetPlusPlus(num_classes=2, in_channels=20).to(device)\n", "\n", "# New stable training setup\n", "criterion, optimizer, scheduler = setup_training(model, train_labels)\n", "scaler = None  # Disable AMP for stability\n", "\n", "# Model info\n", "param_count = sum(p.numel() for p in model.parameters())\n", "print(f\"Model parameters: {param_count:,}\")"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bWsBFpfHQfXB", "outputId": "1fb8895e-9eda-4d38-8637-027908784239"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature 0: range [19.941, 105.057]\n", "Feature 1: range [-0.059, 0.176]\n", "Feature 2: range [0.011, 7.365]\n", "Feature 3: range [0.012, 2.808]\n", "Feature 4: range [-0.214, 3.343]\n", "Feature 5: range [0.118, 12.344]\n", "Feature 6: range [0.051, 9.974]\n", "Feature 7: range [0.011, 5.197]\n", "Feature 8: range [1.046, 3.038]\n", "Feature 9: range [0.754, 3.199]\n", "Feature 10: range [-0.601, 0.668]\n", "Feature 11: range [1.922, 3.902]\n", "Feature 12: range [0.837, 1.474]\n", "Feature 13: range [0.008, 1.873]\n", "Feature 14: range [3.584, 6.305]\n", "Feature 15: range [0.318, 0.350]\n", "Feature 16: range [0.300, 0.333]\n", "Feature 17: range [0.333, 0.364]\n", "Feature 18: range [0.020, 0.674]\n", "Feature 19: range [0.710, 4.444]\n"]}], "source": ["# Inspect feature-wise stats across all patches\n", "all_data = datasets['train']['patches'].reshape(-1, 20)\n", "for i in range(20):\n", "    f_min, f_max = np.min(all_data[:, i]), np.max(all_data[:, i])\n", "    print(f\"Feature {i}: range [{f_min:.3f}, {f_max:.3f}]\")\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ePXt-5A5OlQK", "outputId": "6de231bf-b9de-4618-be35-3c339c793e2c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 10/100: Train Acc=0.6687, Val Acc=0.6866, Train Loss=0.5510, Val Loss=0.6134, LR=0.000500\n", "Epoch 20/100: Train Acc=0.6687, Val Acc=0.6866, Train Loss=0.5243, Val Loss=0.5682, LR=0.000500\n", "Epoch 30/100: Train Acc=0.6742, Val Acc=0.6866, Train Loss=0.4935, Val Loss=0.5239, LR=0.000250\n", "Epoch 40/100: Train Acc=0.7148, Val Acc=0.7177, Train Loss=0.4630, Val Loss=0.4965, LR=0.000250\n", "Epoch 50/100: Train Acc=0.7453, Val Acc=0.7440, Train Loss=0.4600, Val Loss=0.4482, LR=0.000125\n", "Epoch 60/100: Train Acc=0.7562, Val Acc=0.8014, Train Loss=0.4664, Val Loss=0.4445, LR=0.000125\n", "Epoch 70/100: Train Acc=0.7672, Val Acc=0.7560, Train Loss=0.4439, Val Loss=0.4375, LR=0.000063\n", "Epoch 80/100: Train Acc=0.7453, Val Acc=0.7967, Train Loss=0.4786, Val Loss=0.4558, LR=0.000063\n", "Epoch 90/100: Train Acc=0.7789, Val Acc=0.7990, Train Loss=0.4352, Val Loss=0.4373, LR=0.000031\n", "Epoch 100/100: Train Acc=0.7695, Val Acc=0.7919, Train Loss=0.4385, Val Loss=0.4427, LR=0.000031\n", "Training completed in 2793.6s\n", "Best validation accuracy: 0.8230\n", "Best validation loss: 0.4049\n"]}], "source": ["history, best_val_acc, training_time = run_training(\n", "    model, train_loader, val_loader, criterion, optimizer, scheduler,\n", "    config, scaler, models_path\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "G4GAKehvOrQ-"}, "source": ["## Evaluation and Metrics"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"id": "HrNwHwmcO-k0"}, "outputs": [], "source": ["def evaluate_model(model, test_loader, device):\n", "    model.eval()\n", "    all_preds = []\n", "    all_targets = []\n", "\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            all_preds.extend(output.argmax(dim=1).cpu().numpy())\n", "            all_targets.extend(target.cpu().numpy())\n", "\n", "    return all_preds, all_targets\n", "\n", "all_preds, all_targets = evaluate_model(model, test_loader, device)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"id": "QAAASnH7PAlN"}, "outputs": [], "source": ["def compute_metrics(all_preds, all_targets):\n", "    test_accuracy = accuracy_score(all_targets, all_preds)\n", "    print(f\"True Test Accuracy: {test_accuracy:.3f}\")\n", "\n", "    test_f1 = f1_score(all_targets, all_preds)\n", "    test_precision = precision_score(all_targets, all_preds)\n", "    test_recall = recall_score(all_targets, all_preds)\n", "    cm = confusion_matrix(all_targets, all_preds)\n", "\n", "    print(\"\\nTEST METRICS:\")\n", "    print(f\"Accuracy: {test_accuracy:.4f}\")\n", "    print(f\"F1-Score: {test_f1:.4f}\")\n", "    print(f\"Precision: {test_precision:.4f}\")\n", "    print(f\"Recall: {test_recall:.4f}\")\n", "    print(\"Confusion Matrix:\\n\", cm)\n", "\n", "    return {\n", "        'accuracy': test_accuracy,\n", "        'f1_score': test_f1,\n", "        'precision': test_precision,\n", "        'recall': test_recall,\n", "        'confusion_matrix': cm.tolist()\n", "    }"]}, {"cell_type": "markdown", "metadata": {"id": "8oRw1IkYPCDX"}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 407}, "id": "niKwJBzuQKlb", "outputId": "8f6a5073-a328-4459-9f38-7107b2f3717a"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot training and validation loss\n", "plt.figure(figsize=(10, 4))\n", "plt.subplot(1, 2, 1)\n", "\n", "plt.plot(history['train_losses'], label='Train Loss')\n", "plt.plot(history['val_losses'], label='Val Loss')\n", "plt.title(\"Loss\")\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history['train_accs'], label='Train Acc')\n", "plt.plot(history['val_accs'], label='Val Acc')\n", "plt.title(\"Accuracy\")\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 739}, "id": "14kUcTxpQZbu", "outputId": "31274c51-87f6-4bf8-bd09-70691909b2ba"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Test Accuracy: 0.809\n", "\n", "TEST METRICS:\n", "Accuracy: 0.8086\n", "F1-Score: 0.8639\n", "Precision: 0.8439\n", "Recall: 0.8850\n", "Confusion Matrix:\n", " [[ 84  47]\n", " [ 33 254]]\n", "Confusion Matrix:\n", "[[ 84  47]\n", " [ 33 254]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Confusion matrix\n", "from sklearn.metrics import confusion_matrix\n", "import seaborn as sns\n", "\n", "metrics_dict = compute_metrics(all_preds, all_targets)\n", "\n", "cm = confusion_matrix(all_targets, all_preds)\n", "print(\"Confusion Matrix:\")\n", "print(cm)\n", "\n", "plt.figure(figsize=(6, 5))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap=\"Blues\", xticklabels=[\"Non-Pile\", \"<PERSON>le\"], yticklabels=[\"Non-Pile\", \"Pile\"])\n", "plt.xlabel(\"Predicted\")\n", "plt.ylabel(\"Actual\")\n", "plt.title(\"Confusion Matrix\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 878}, "id": "lLsn9x08QbZg", "outputId": "7581ac3e-9c87-4333-978f-c79860549e87"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reconstructed 287 coordinates from pile IDs\n", "Spatial subset accuracy: 0.885 (287 samples)\n", "Full test set accuracy: 0.809 (418 samples)\n", "Coordinate range: X[435305, 436682], Y[5010922, 5012461]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle\n", "from sklearn.metrics import accuracy_score\n", "\n", "def create_pointnet_spatial_plot_correct_way(all_preds, all_targets, test_pkl_path, results_path, metrics):\n", "    \"\"\"Create PointNet++ spatial plot using the same method as classical ML models.\"\"\"\n", "\n", "    # Load reference data\n", "    ifc_df = pd.read_csv(ifc_path)\n", "    ifc_coords = ifc_df[['X', 'Y']].values\n", "\n", "    gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "    gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)\n", "    gdf_kml['geometry'] = gdf_kml.geometry.centroid\n", "    kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)\n", "\n", "    # Load the original pile dataset\n", "    pile_df = pd.read_csv(harmonized_pile_dataset)\n", "\n", "    # Load test metadata\n", "    with open(test_pkl_path, 'rb') as f:\n", "        test_data = pickle.load(f)\n", "    test_metadata = test_data.get('metadata', [])\n", "\n", "    # Reconstruct coordinates using pile IDs\n", "    pred_coords = []\n", "    valid_indices = []\n", "\n", "    for i, meta in enumerate(test_metadata[:len(all_preds)]):\n", "        pile_id = meta.get('pile_id') if isinstance(meta, dict) else None\n", "\n", "        if pile_id and pile_id in pile_df['pile_id'].values:\n", "            pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]\n", "            pred_coords.append([pile_row['x'], pile_row['y']])\n", "            valid_indices.append(i)\n", "\n", "    if len(pred_coords) == 0:\n", "        print(\"ERROR: No pile IDs found in metadata!\")\n", "        return\n", "\n", "    pred_coords = np.array(pred_coords)\n", "\n", "    # Filter predictions to match valid coordinates\n", "    all_preds_filtered = [all_preds[i] for i in valid_indices]\n", "    all_targets_filtered = [all_targets[i] for i in valid_indices]\n", "\n", "    # Calculate accuracy for SPATIAL subset (for display purposes)\n", "    correct_mask = (np.array(all_targets_filtered) == np.array(all_preds_filtered))\n", "    error_mask = ~correct_mask\n", "    spatial_accuracy = np.mean(correct_mask)\n", "\n", "    # Use the TRUE accuracy from full test set for title\n", "    true_accuracy = metrics['accuracy']\n", "\n", "    print(f\"Reconstructed {len(pred_coords)} coordinates from pile IDs\")\n", "    print(f\"Spatial subset accuracy: {spatial_accuracy:.3f} ({len(pred_coords)} samples)\")\n", "    print(f\"Full test set accuracy: {true_accuracy:.3f} ({len(all_preds)} samples)\")\n", "    print(f\"Coordinate range: X[{pred_coords[:, 0].min():.0f}, {pred_coords[:, 0].max():.0f}], Y[{pred_coords[:, 1].min():.0f}, {pred_coords[:, 1].max():.0f}]\")\n", "\n", "    # Create plot exactly like classical ML models\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "\n", "    # Plot reference data\n", "    ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1],\n", "              s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')\n", "    ax.scatter(kml_coords[:, 0], kml_coords[:, 1],\n", "              s=8, alpha=0.7, color='green', marker='s', label='KML Ground Truth')\n", "\n", "    # Plot predictions\n", "    if np.sum(correct_mask) > 0:\n", "        ax.scatter(pred_coords[correct_mask, 0], pred_coords[correct_mask, 1],\n", "                  c='blue', s=30, alpha=0.8, marker='o',\n", "                  label=f'Correct ({np.sum(correct_mask)})')\n", "\n", "    if np.sum(error_mask) > 0:\n", "        ax.scatter(pred_coords[error_mask, 0], pred_coords[error_mask, 1],\n", "                  c='red', s=50, alpha=0.9, marker='x', linewidth=2,\n", "                  label=f'Errors ({np.sum(error_mask)})')\n", "\n", "    # Use TRUE accuracy in title, but show spatial stats in legend\n", "    ax.set_title(f'PointNet++\\nAccuracy: {true_accuracy:.3f}')\n", "    ax.set_xlabel('X (UTM)')\n", "    ax.set_ylabel('Y (UTM)')\n", "    ax.legend(fontsize=8)\n", "    ax.grid(True, alpha=0.3)\n", "    ax.set_aspect('equal', adjustable='box')\n", "\n", "    # Add text box with full stats\n", "    stats_text = f'Full Test Set:\\n{len(all_preds)} samples\\n{true_accuracy:.1%} accuracy\\n\\nSpatial Subset:\\n{len(pred_coords)} samples\\n{spatial_accuracy:.1%} accuracy'\n", "    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,\n", "            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "\n", "    plt.tight_layout()\n", "    plt.savefig(results_path / 'pointnet_spatial_final_corrected.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n", "\n", "    return {\n", "        'spatial_accuracy': spatial_accuracy,\n", "        'spatial_samples': len(pred_coords),\n", "        'true_accuracy': true_accuracy,\n", "        'total_samples': len(all_preds)\n", "    }\n", "\n", "spatial_results = create_pointnet_spatial_plot_correct_way(\n", "    all_preds, all_targets, test_pkl_path, results_path, metrics_dict\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "x18jbwZRQfug"}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "g0v3DMTiQhuL", "outputId": "57e3bcf6-c3ed-43d2-dd81-32e0e366e86a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results saved to: /content/drive/MyDrive/pointnet_pile_detection/results_iter2/pointnet_sa_radius_classification.json\n"]}], "source": ["def save_results(results_path, metrics, history, config, best_val_acc, training_time, preds, targets):\n", "    results = {\n", "        'iteration': 1,\n", "        'model': 'Basic PointNet++',\n", "        'architecture': 'Simplified layers with k-NN grouping',\n", "        'training_time': training_time,\n", "        'best_val_acc': best_val_acc,\n", "        'test_metrics': metrics,\n", "        'config': config,\n", "        'training_history': history,\n", "        'predictions': preds,\n", "        'ground_truth': targets\n", "    }\n", "\n", "    with open(results_path / 'pointnet_sa_radius_classification.json', 'w') as f:\n", "        json.dump(results, f, indent=2, default=str)\n", "\n", "    print(f\"Results saved to: {results_path / 'pointnet_sa_radius_classification.json'}\")\n", "\n", "save_results(results_path, metrics_dict, history, config, best_val_acc, training_time, all_preds, all_targets)\n"]}, {"cell_type": "markdown", "metadata": {"id": "U9hoP3P4XkkF"}, "source": ["## Previous Iteration Comparision"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"id": "WyILW2-MJtMC"}, "outputs": [], "source": ["results_path_1 = Path(\"/content/drive/MyDrive/pointnet_pile_detection/results_iter1\")\n", "results_path_2 = Path(\"/content/drive/MyDrive/pointnet_pile_detection/results_iter2\")\n", "\n", "def load_iteration_results():\n", "    iter1_path = results_path_1 / \"pointnet_knn_classification.json\"\n", "    iter2_path = results_path_2 / \"pointnet_sa_radius_classification.json\"\n", "\n", "    if iter1_path.exists() and iter2_path.exists():\n", "        with open(iter1_path, 'r') as f:\n", "            iter1 = json.load(f)\n", "            if isinstance(iter1, str):\n", "                iter1 = json.loads(iter1)\n", "\n", "        with open(iter2_path, 'r') as f:\n", "            iter2 = json.load(f)\n", "            if isinstance(iter2, str):\n", "                iter2 = json.loads(iter2)\n", "\n", "        return iter1, iter2\n", "    else:\n", "        print(\"Iteration result files not found.\")\n", "        return None, None\n", "\n", "def plot_comparison(iter1, iter2):\n", "    import matplotlib.pyplot as plt\n", "    import numpy as np\n", "\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))\n", "\n", "    # Training Accuracy\n", "    ax1.plot(iter1['training_history']['train_accs'], label='Iteration 1 (k-NN)', alpha=0.7)\n", "    ax1.plot(iter2['training_history']['train_accs'], label='Iteration 2 (Set Abstraction)', alpha=0.7)\n", "    ax1.set_title('Training Accuracy')\n", "    ax1.set_xlabel('Epoch')\n", "    ax1.set_ylabel('Accuracy')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "\n", "    # Validation Accuracy\n", "    ax2.plot(iter1['training_history']['val_accs'], label='Iteration 1 (k-NN)', alpha=0.7)\n", "    ax2.plot(iter2['training_history']['val_accs'], label='Iteration 2 (Set Abstraction)', alpha=0.7)\n", "    ax2.set_title('Validation Accuracy')\n", "    ax2.set_xlabel('Epoch')\n", "    ax2.set_ylabel('Accuracy')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "\n", "    # Learning Rate Schedule (Iteration 2 only)\n", "    if 'learning_rates' in iter2['training_history']:\n", "        ax3.plot(iter2['training_history']['learning_rates'])\n", "        ax3.set_title('Learning Rate Schedule (Iteration 2)')\n", "        ax3.set_xlabel('Epoch')\n", "        ax3.set_ylabel('Learning Rate')\n", "        ax3.grid(True, alpha=0.3)\n", "    else:\n", "        ax3.axis(\"off\")\n", "\n", "    # Test Metrics Comparison\n", "    metrics = ['accuracy', 'f1_score', 'precision', 'recall']\n", "    iter1_values = [iter1['test_metrics'][m] for m in metrics]\n", "    iter2_values = [iter2['test_metrics'][m] for m in metrics]\n", "\n", "    x = np.arange(len(metrics))\n", "    width = 0.35\n", "\n", "    ax4.bar(x - width/2, iter1_values, width, label='Iteration 1', alpha=0.7)\n", "    ax4.bar(x + width/2, iter2_values, width, label='Iteration 2', alpha=0.7)\n", "    ax4.set_title('Test Metrics Comparison')\n", "    ax4.set_ylabel('Score')\n", "    ax4.set_xticks(x)\n", "    ax4.set_xticklabels([m.replace('_', ' ').title() for m in metrics])\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "\n", "    # Save and Show\n", "    plt.tight_layout()\n", "    plt.savefig(results_path / 'comparison_plot.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "    print(f\"Comparison plot saved to: {results_path / 'comparison_plot.png'}\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "Dd-KrmX3Zfve", "outputId": "95a5d9c7-1a69-4c56-a315-da06942b7d1e"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Comparison plot saved to: /content/drive/MyDrive/pointnet_pile_detection/results_iter2/comparison_plot.png\n"]}], "source": ["iter1, iter2 = load_iteration_results()\n", "\n", "if iter1 and iter2:\n", "    plot_comparison(iter1, iter2)"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}