{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Multi-Site PointNet++ Pile Verification Runner\n", "\n", "This notebook uses Papermill to execute the PointNet++ pile verification notebook across multiple construction sites.\n", "\n", "**Workflow:**\n", "1. Configure multiple sites with their point cloud and expected pile coordinate paths\n", "2. Execute pile verification notebook for each site using Papermill\n", "3. Generate separate executed notebooks for each site\n", "4. Provide summary of verification results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis - Multi-Site Pile Verification"]}, {"cell_type": "code", "execution_count": 65, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports completed successfully\n"]}], "source": ["import os\n", "import sys\n", "import papermill as pm\n", "from pathlib import Path\n", "from datetime import datetime\n", "import pandas as pd\n", "\n", "print(\"Imports completed successfully\")"]}, {"cell_type": "code", "execution_count": 66, "id": "configuration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configured 3 sites for inference:\n", "  1. trino_enel: Trino ENEL site with Point_Cloud.las\n", "  2. althea_rpcs: Althea RPCS site with Point_Cloud.las\n", "  3. nortan_res: Nortan RES site with Block_11_2m.las\n"]}], "source": ["# Site configurations for inference\n", "SITE_CONFIGS = [\n", "    {\n", "        \"site_name\": \"trino_enel\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\",\n", "        \"description\": \"Trino ENEL site with Point_Cloud.las\",\n", "        \"crs\": \"EPSG:32632\",  # UTM Zone 32N\n", "        \"expected_coord_range\": {\n", "            \"x_min\": 435200, \"x_max\": 436800,\n", "            \"y_min\": 5010800, \"y_max\": 5012600\n", "        },\n", "        \"fast_mode\": True  # Fast mode for thesis sites\n", "    },\n", "    {\n", "        \"site_name\": \"althea_rpcs\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\",\n", "        \"description\": \"Althea RPCS site with Point_Cloud.las\",\n", "        \"crs\": \"EPSG:32615\",  # UTM Zone 15N\n", "        \"expected_coord_range\": {\n", "            \"x_min\": 599000, \"x_max\": 600000,\n", "            \"y_min\": 4334000, \"y_max\": 4335000\n", "        }\n", "    },\n", "    {\n", "        \"site_name\": \"nortan_res\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\",\n", "        \"description\": \"Nortan RES site with Block_11_2m.las\", \n", "        \"crs\": \"EPSG:32614\",  # UTM Zone 14N\n", "        \"expected_coord_range\": {\n", "            \"x_min\": 400000, \"x_max\": 500000,  # Adjust based on actual data\n", "            \"y_min\": 4300000, \"y_max\": 4400000\n", "        }\n", "    }\n", "]\n", "\n", "# Base parameters for all runs\n", "BASE_PARAMETERS = {\n", "    \"MODEL_PATH\": \"best_pointnet_plus_plus.pth\",\n", "    \"DWG_PATH\": \"\",\n", "    \"CONFIDENCE_THRESHOLD\": 0.95,\n", "    \"BATCH_SIZE\": 16,\n", "    \"GRID_SPACING\": 5.0,\n", "    \"PATCH_SIZE\": 3.0,\n", "    \"NUM_POINTS\": 128,\n", "    \"EXPERIMENT_NAME\": \"pointnet_plus_plus_inference\",\n", "    \"SITE_CRS\": None  # Will be set per site\n", "}\n", "print(f\"Configured {len(SITE_CONFIGS)} sites for inference:\")\n", "for i, config in enumerate(SITE_CONFIGS, 1):\n", "    print(f\"  {i}. {config['site_name']}: {config['description']}\")"]}, {"cell_type": "code", "execution_count": 67, "id": "validation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validating file paths...\n", "All paths validated successfully\n", "Output directory ready\n"]}], "source": ["def validate_paths():\n", "    \"\"\"Validate that required files exist before execution\"\"\"\n", "    issues = []\n", "    \n", "    # Check if base notebook exists\n", "    base_notebook = \"04_pointnet_plus_plus_inference.ipynb\"\n", "    if not Path(base_notebook).exists():\n", "        issues.append(f\"Base notebook not found: {base_notebook}\")\n", "    \n", "    # Check if model exists\n", "    model_path = BASE_PARAMETERS[\"MODEL_PATH\"]\n", "    if not Path(model_path).exists():\n", "        issues.append(f\"Model file not found: {model_path}\")\n", "    \n", "    # Check point cloud files\n", "    for config in SITE_CONFIGS:\n", "        pc_path = config[\"point_cloud_path\"]\n", "        if not Path(pc_path).exists():\n", "            issues.append(f\"Point cloud not found for {config['site_name']}: {pc_path}\")\n", "    \n", "    return issues\n", "\n", "# Validate paths before starting\n", "print(\"Validating file paths...\")\n", "validation_issues = validate_paths()\n", "\n", "if validation_issues:\n", "    print(\"Validation failed:\")\n", "    for issue in validation_issues:\n", "        print(f\"  - {issue}\")\n", "    print(\"\\nPlease fix these issues before proceeding.\")\n", "else:\n", "    print(\"All paths validated successfully\")\n", "    \n", "# Create output directory if it doesn't exist\n", "os.makedirs(\"output_runs/pointnet_plus_plus_inference\", exist_ok=True)\n", "print(\"Output directory ready\")"]}, {"cell_type": "code", "execution_count": 68, "id": "execution_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Inference runner ready.\n"]}], "source": ["import os\n", "\n", "def run_site_inference(site_config):\n", "    \"\"\"Run inference notebook for a given site using papermill shell command.\"\"\"\n", "    site = site_config[\"site_name\"]\n", "    input_nb = \"04_pointnet_plus_plus_inference.ipynb\"\n", "    output_nb = f\"04_pointnet_plus_plus_inference_{site}_executed.ipynb\"\n", "    output_dir = f\"output_runs/pointnet_plus_plus_inference/{site}\"\n", "    use_fast_mode = site_config.get(\"fast_mode\", False)\n", "\n", "    print(f\"\\n--- Running inference: {site} ---\")\n", "\n", "    cmd = (\n", "        f'papermill {input_nb} {output_nb} '\n", "        f'-p NEW_SITE_NAME \"{site}\" '\n", "        f'-p POINT_CLOUD_PATH \"{site_config[\"point_cloud_path\"]}\" '\n", "        f'-p SITE_CRS \"{site_config[\"crs\"]}\" '  \n", "        f'-p USE_FAST_MODE {str(use_fast_mode).lower()} '\n", "        f'-p OUTPUT_DIR \"{output_dir}\" '\n", "        f'-p RUN_NAME \"inference_{site}\" '\n", "        '--log-output --kernel pytorch-geo-dev'\n", "    )\n", "\n", "    try:\n", "        if os.system(cmd) == 0:\n", "            print(f\"✓ Completed: {site}\")\n", "            return True, None\n", "        else:\n", "            msg = f\"Papermill failed for {site}\"\n", "            print(msg)\n", "            return False, msg\n", "    except Exception as e:\n", "        print(f\"Exception: {e}\")\n", "        return False, str(e)\n", "\n", "print(\"Inference runner ready.\")"]}, {"cell_type": "code", "execution_count": 69, "id": "execute_sites", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting multi-site inference...\n", "==================================================\n", "\n", "--- Running inference: trino_enel ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Input Notebook:  04_pointnet_plus_plus_inference.ipynb\n", "Output Notebook: 04_pointnet_plus_plus_inference_trino_enel_executed.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "New Site Analysis Configuration:\n", "Site: trino_enel\n", "Using fast mode: true\n", "EPSG Configuration:\n", "  Inference EPSG: EPSG:32632\n", "Patch Size: 20.0m radius\n", "Points per Patch: 1024\n", "Model Path: best_pointnet_iter4.pth\n", "Point Cloud Path: ../../../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\n", "Output Directory: output_runs/pointnet_plus_plus_inference/trino_enel\n", "Extended Analysis: True\n", "Full Site Analysis: True\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "No handler found for comm target 'dash'\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "Starting PointNet++ Pile Detection Pipeline\n", "Site: trino_enel\n", "Configuration: 20.0m patches, 1024 points each\n", "Using device: cpu\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Loaded checkpoint from epoch 97\n", "Loaded trained PointNet++ model with 1,482,434 parameters\n", "\n", "Loaded LAS file with 282,518,678 points\n", "Point cloud bounds:\n", "\n", "  X: 435219.87 to 436795.75\n", "\n", "  Y: 5010811.23 to 5012553.00\n", "\n", "  Z: -7.02 to 31.14\n", "\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "Using fast demo grid for thesis demonstration\n", "Created thesis demo grid: 625 points\n", "Final grid size: 625 points\n", "Processing with 512 points per patch, batch size 8\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "Running fast inference for thesis demonstration\n", "Processing 500 points for thesis demo\n", "Building spatial index for 282,518,678 points...\n", "\n", "Pre-filtering valid grid points...\n", "  Pre-filtering progress: 0/500\n", "\n", "Filtered to 500 valid grid points (from 500)\n", "Processing 500 points with 1024 points/patch (CPU optimized)...\n", "This maintains training accuracy but will be slower than 512 points\n", "  Batch 1/63 (0/500 points)\n", "\n", "  <PERSON><PERSON> 4/63 (24/500 points)\n", "\n", "  <PERSON><PERSON> 7/63 (48/500 points)\n", "\n", "  <PERSON>ch 10/63 (72/500 points)\n", "\n", "  <PERSON><PERSON> 13/63 (96/500 points)\n", "\n", "  Batch 16/63 (120/500 points)\n", "\n", "  Batch 19/63 (144/500 points)\n", "\n", "  Batch 22/63 (168/500 points)\n", "\n", "  <PERSON>ch 25/63 (192/500 points)\n", "\n", "  Batch 28/63 (216/500 points)\n", "\n", "  Batch 31/63 (240/500 points)\n", "\n", "  Batch 34/63 (264/500 points)\n", "\n", "  <PERSON><PERSON> 37/63 (288/500 points)\n", "\n", "  <PERSON>ch 40/63 (312/500 points)\n", "\n", "  Batch 43/63 (336/500 points)\n", "\n", "  <PERSON>ch 46/63 (360/500 points)\n", "\n", "  Batch 49/63 (384/500 points)\n", "\n", "  Batch 52/63 (408/500 points)\n", "\n", "  Batch 55/63 (432/500 points)\n", "\n", "  Batch 58/63 (456/500 points)\n", "\n", "  Batch 61/63 (480/500 points)\n", "\n", "CPU processing complete: 500 successful predictions\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "\n", "Analysis Results:\n", "  Total analysis points: 500\n", "  Pile detections: 500 (100.0%)\n", "  Average confidence: 1.0000\n", "\n", "Saved visualization to: output_runs/pointnet_plus_plus_inference/trino_enel/trino_enel_pile_visualization.png\n", "\n", "<Figure size 1500x600 with 3 Axes>\n", "Results exported to: output_runs/pointnet_plus_plus_inference/trino_enel/trino_enel_pile_detections_20250807_160826.csv\n", "Summary statistics saved to: output_runs/pointnet_plus_plus_inference/trino_enel/trino_enel_analysis_summary_20250807_160826.json\n", "\n", "Pipeline complete. Results saved to: output_runs/pointnet_plus_plus_inference/trino_enel\n", "\n", "Ending Cell 25-----------------------------------------\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ Completed: trino_enel\n", "\n", "Total execution time: 0:14:10.657307\n"]}], "source": ["INCLUDE_SITES = [\"trino_enel\"]\n", "\n", "# Filter SITE_CONFIGS based on INCLUDE_SITES\n", "filtered_configs = [cfg for cfg in SITE_CONFIGS if cfg[\"site_name\"] in INCLUDE_SITES]\n", "\n", "if validation_issues:\n", "    print(\"Skipping execution due to validation errors.\")\n", "    results = []\n", "else:\n", "    print(\"Starting multi-site inference...\\n\" + \"=\" * 50)\n", "    start_time = datetime.now()\n", "\n", "    results = [\n", "        {\n", "            \"site\": cfg[\"site_name\"],\n", "            \"description\": cfg[\"description\"],\n", "            \"success\": (res := run_site_inference(cfg))[0],\n", "            \"error\": res[1],\n", "            \"output_notebook\": f\"04_pointnet_plus_plus_inference_{cfg['site_name']}_executed.ipynb\"\n", "        }\n", "        for cfg in filtered_configs\n", "    ]\n", "\n", "    print(f\"\\nTotal execution time: {datetime.now() - start_time}\")\n"]}, {"cell_type": "code", "execution_count": 70, "id": "summary_report", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "EXECUTION SUMMARY\n", "============================================================\n", "Total sites: 1 | Success: 1 | Failed: 0\n", "\n", "trino_enel: SUCCESS\n", "\n", "Generated Notebooks:\n", "============================================================\n", "  04_pointnet_plus_plus_inference_trino_enel_executed.ipynb (0.2 MB)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>description</th>\n", "      <th>status</th>\n", "      <th>output_notebook</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trino_enel</td>\n", "      <td>Trino ENEL site with Point_Cloud.las</td>\n", "      <td>SUCCESS</td>\n", "      <td>04_pointnet_plus_plus_inference_trino_enel_exe...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         site                           description   status  \\\n", "0  trino_enel  Trino ENEL site with Point_Cloud.las  SUCCESS   \n", "\n", "                                     output_notebook  \n", "0  04_pointnet_plus_plus_inference_trino_enel_exe...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Completed! Results saved to: multi_site_inference_results.csv\n"]}], "source": ["if results:\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"EXECUTION SUMMARY\")\n", "    print(\"=\"*60)\n", "\n", "    success_count = sum(r[\"success\"] for r in results)\n", "    fail_count = len(results) - success_count\n", "\n", "    print(f\"Total sites: {len(results)} | Success: {success_count} | Failed: {fail_count}\\n\")\n", "\n", "    for r in results:\n", "        status = \"SUCCESS\" if r[\"success\"] else f\"FAILED\\n    Error: {r['error']}\"\n", "        print(f\"{r['site']}: {status}\")\n", "\n", "    print(\"\\nGenerated Notebooks:\")\n", "    print(\"=\"*60)\n", "    for r in results:\n", "        nb_path = Path(r[\"output_notebook\"])\n", "        size = f\"{nb_path.stat().st_size / (1024 * 1024):.1f} MB\" if nb_path.exists() else \"not found\"\n", "        print(f\"  {nb_path.name} ({size})\")\n", "\n", "    # Display and save results\n", "    df = pd.DataFrame(results)\n", "    df[\"status\"] = df[\"success\"].map({True: \"SUCCESS\", False: \"FAILED\"})\n", "    display(df[[\"site\", \"description\", \"status\", \"output_notebook\"]])\n", "    \n", "    csv_path = \"multi_site_inference_results.csv\"\n", "    df.to_csv(csv_path, index=False)\n", "    print(f\"\\nCompleted! Results saved to: {csv_path}\")\n", "else:\n", "    print(\"No results to display due to validation or execution failure.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}